import { GameComponent, SpriteRendererComponent } from '@/features/gameplay/shared';

export class IndexZSetterComponent extends GameComponent {
  private spriteRenderer?: SpriteRendererComponent;

  override attach(gameObject: import('@/features/gameplay/shared').GameObject): void {
    super.attach(gameObject);
    this.spriteRenderer = this.gameObject.getComponent(SpriteRendererComponent);
  }

  override update(): void {
    if (!this.spriteRenderer) return;

    const yNormalized = this.gameObject.transform.position.y;

    // Convert normalized y into a zIndex (higher y -> higher zIndex)
    // Multiply by 10000 for sorting granularity
    const zIndex = Math.floor(yNormalized * 100);

    this.spriteRenderer.sprite.zIndex = zIndex;
  }
}
