import { createI18n } from 'vue-i18n';

// Function to get system language and map to supported locales
function getSystemLanguage() {
  const systemLang = navigator.language || navigator.userLanguage || 'en';
  const langCode = systemLang.toLowerCase().split('-')[0]; // Get primary language code
  
  // Map system language to supported locales
  const supportedLocales = ['en', 'ja', 'zh'];
  const languageMap = {
    'ja': 'ja',     // Japanese
    'zh': 'zh',     // Chinese (Simplified)
    'zh-cn': 'zh',  // Chinese (China)
    'zh-tw': 'zh',  // Chinese (Taiwan)
    'zh-hk': 'zh',  // Chinese (Hong Kong)
    'en': 'en',     // English
    'en-us': 'en',  // English (US)
    'en-gb': 'en',  // English (UK)
  };
  
  return languageMap[langCode] || languageMap[systemLang] || 'en';
}

// Get saved language or fall back to system language
export const savedLanguage = localStorage.getItem('app_language') || getSystemLanguage();

const i18n = createI18n({
  legacy: false,
  locale: savedLanguage,
  fallbackLocale: 'en',
  messages: {}, // start empty
  globalInjection: true,
});

export async function loadLocaleMessages(locale) {
  if (!i18n.global.availableLocales.includes(locale)) {
    try {
      const response = await fetch(`/locales/${locale}.json`);
      const messages = await response.json();
      i18n.global.setLocaleMessage(locale, messages);
    } catch (error) {
      console.error(`Failed to load locale ${locale}:`, error);
    }
  }

  i18n.global.locale.value = locale;
  localStorage.setItem('app_language', locale);
}

export default i18n;
