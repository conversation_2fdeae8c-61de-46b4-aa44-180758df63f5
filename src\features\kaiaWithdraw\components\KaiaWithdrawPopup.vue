<script setup>
import { AppPopup, TitlePanelColor, AppModal, CloseButton } from '@/components/common'
import { useKaiaWithdrawPopupStore } from '../stores/kaiaWithdrawPopupStore'

const kaiaWithdrawPopupStore = useKaiaWithdrawPopupStore()

</script>

<template>
  <AppPopup :isOpen="kaiaWithdrawPopupStore.isKaiaWithdrawShow" @close="kaiaWithdrawPopupStore.closeKaiaWithdrawPopup">
    <template #modal>
        <div class="modal-container">
            <AppModal class="popup-modal">
                <CloseButton class="close-button" @click="kaiaWithdrawPopupStore.closeKaiaWithdrawPopup" />
                <div class="popup-content">
                    <TitlePanelColor class="title">{{ $t('common.kaiaWithdraw') }}</TitlePanelColor>
                </div>
            </AppModal>
        </div>
    </template>
  </AppPopup>
</template>

<style scoped>
</style>
