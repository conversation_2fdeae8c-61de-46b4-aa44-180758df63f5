import { Assets } from 'pixi.js';

export interface FarmPlotAssets {
  background: string;
  cow: string;
  colorTint: number;
}

export class AssetService {
  private static readonly ASSET_PATHS = {
    backgrounds: {
      level0: '/assets/farmPlot/farm-plot-1.png',
      level1: '/assets/farmPlot/farm-plot-2.png',
      level2: '/assets/farmPlot/farm-plot-3.png',
      level3: '/assets/farmPlot/farm-plot-4.png',
    },
    cows: {
      level0: '/assets/cows/cow-1.png',
      level1: '/assets/cows/cow-2.png',
      level2: '/assets/cows/cow-3.png',
      level3: '/assets/cows/cow-4.png'
    }
  };

  private static readonly COLOR_TINTS = {
    level0: 0xFFFFFF, // White
    level1: 0xADD8E6, // Light blue
    level2: 0x98FB98, // Light green
    level3: 0xFFFFB3, // Light yellow
    level4: 0xFFDAAB, // Light orange
    level5: 0xFFB6B6, // Light red
    level6: 0xE6B3FF, // Light purple
    level7: 0xD3D3D3  // Light gray (instead of black)
  };

  static async loadPlotAssets(plotNumber: number): Promise<FarmPlotAssets> {
    const cowLevel = `level${(plotNumber - 1) % 4}`;
    const bgLevel = `level${Math.floor((plotNumber - 1) / 4) % 4}`;
    const colorLevel = `level${Math.floor((plotNumber - 1) / 16) % 8}`;
  
    // Load the assets
    await Assets.load([
      this.ASSET_PATHS.backgrounds[bgLevel],
      this.ASSET_PATHS.cows[cowLevel]
    ]);
  
    return {
      background: this.ASSET_PATHS.backgrounds[bgLevel],
      cow: this.ASSET_PATHS.cows[cowLevel],
      colorTint: this.COLOR_TINTS[colorLevel]
    };
  }
  
} 