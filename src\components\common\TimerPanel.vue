<template>
    <div class="timer-component">
      <div class="icon">
        <img :src="'/icon/timer.png'" alt="timer icon" />
      </div>
      
      <div class="content-container">
        <span class="label-section">
            <slot>
                <div class="label-text">{{ $t('common.timer') }}</div>
            </slot>
        </span>
        <span class="time-section">
            <slot>
                <div class="label-text">00:00:00</div>
            </slot>
        </span>
      </div>

    </div>
  </template>
  
  <script>
  import { useI18n } from 'vue-i18n'
  
  export default {
    name: 'GameTimer',
    props: {
      label: {
        type: String,
        default: 'Timer'
      },
      hours: {
        type: Number,
        default: 99
      },
      minutes: {
        type: Number,
        default: 99
      },
      seconds: {
        type: Number,
        default: 99
      }
    },
    setup() {
      const { t } = useI18n()
      return { t }
    },
    computed: {
      formattedTime() {
        const formatNumber = (num) => String(num).padStart(2, '0');
        return `${formatNumber(this.hours)}:${formatNumber(this.minutes)}:${formatNumber(this.seconds)}`;
      }
    }
  }
  </script>
  
  <style scoped>
  .timer-component {
    position: relative;
    display: flex;
    align-items: center;
    height: calc(var(--base-unit) * 38);
  }
  
  .icon {
    position: absolute;
    left: 0;
    z-index: 2;
    width: calc(var(--base-unit) * 38);
    height: calc(var(--base-unit) * 38);
    flex-shrink: 0;
  }
  
  .icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
  
  .content-container {
    position: relative;

    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(var(--base-unit) * 24);

    transform: skew(-10deg);

    border-radius: calc(var(--base-unit) * 4);
    margin-left: calc(var(--base-unit) * 12);

    left: calc(var(--base-unit) * 19); /* Half the icon width */
    z-index: 1;
  }

  .label-section {
  display: flex;
  align-items: center;
  background-color: rgba(26, 26, 26, 1);
  padding: calc(var(--base-unit) * 0) calc(var(--base-unit) * 8);
  height: 100%;
  z-index: 1;

  border: calc(var(--base-unit) * 2) solid black;
  border-right: none;
}

  .time-section {
  display: flex;
  align-items: center;
  background-image: linear-gradient(to right, #F6F323, #CEB50B);
  padding: calc(var(--base-unit) * 0) calc(var(--base-unit) * 8);
  height: 100%;
  border-top-right-radius: calc(var(--base-unit) * 4);
  border-bottom-right-radius: calc(var(--base-unit) * 4);

  border: calc(var(--base-unit) * 2) solid black;
  border-left: none;
}

.label-text {
    font-size: calc(var(--base-unit) * 12);
    -webkit-text-stroke: calc(var(--base-unit) * 1) rgba(59, 59, 59, 1);
    transform: skew(10deg);
    text-shadow: none;
    white-space: nowrap;
}
  </style>