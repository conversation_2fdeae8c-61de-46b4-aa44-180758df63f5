// features/userInfo/composables/useUserInfo.js
import { ref } from 'vue'
import { useNotificationStore } from '@/stores/notificationStore.js';
import { getFreeTicketRemainingLimit, transferTicket as transferTicketAPI  } from '../api'

const remainingLimit = ref(0)

export const useTicketTransfer = () => {
  const fetchRemainingLimit = async () => {
    const { data } = await getFreeTicketRemainingLimit();
    console.log("getFreeTicketRemainingLimit:", data);
    remainingLimit.value = data.remainingLimit;
  }

  const transferTicket = async (recipient, amount) => {
    try {
      const { data } = await transferTicketAPI(recipient, amount)

      useNotificationStore().addNotification({
        type: data.ok ? 'success' : 'error',
        message: data.message || 'Something went wrong.',
        duration: 3000
      })

      await fetchRemainingLimit()
    } catch (err) {
      console.error('API Error:', err)

      useNotificationStore().addNotification({
        type: 'error',
        message:
          err?.response?.data?.message ||
          err?.message ||
          'Something went wrong.',
        duration: 3000
      })
    }
  }

  return {
    remainingLimit,
    transferTicket,
    fetchRemainingLimit,
  }
}
