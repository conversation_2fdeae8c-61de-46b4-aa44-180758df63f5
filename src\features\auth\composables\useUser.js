import { ref } from "vue";

const user = ref(null);

export const useUser = () => {
  async function setUser(payload) {
    user.value = payload;
    localStorage.setItem("user", JSON.stringify(payload))
  }

  function loadUser() {
    const raw = localStorage.getItem("user")
    if (raw) user.value = JSON.parse(raw)
  }

  function remove() {
    user.value = null
    localStorage.removeItem("user")
  }

  const isWalletMatch = (connectedWallet) => {
    const cached = user.value?.walletAddress?.toLowerCase()
    const current = connectedWallet?.toLowerCase()
    return cached && current && cached === current
  }


  return {
    user,
    setUser,
    loadUser,
    remove,
    isWalletMatch
  };
}