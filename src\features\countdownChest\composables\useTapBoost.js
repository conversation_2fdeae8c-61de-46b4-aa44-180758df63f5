// features/countdownChest/composables/useTapBoost.js
import { ref } from 'vue'

export const useTapBoost = ({
  onTap,
  onAccelerate,
  accelerateCooldown = 500,
  canTap = () => true
}) => {
  const tapCount = ref(0)
  const isTapped = ref(false)
  const showPopup = ref(false)
  const popupTimer = ref(null)
  const timeFlash = ref(false)
  const tapResetTimer = ref(null)

  const triggerTap = async () => {
    if (!canTap()) return
    tapCount.value++
    await onTap();

    // Reset and retrigger tap animation
    isTapped.value = false
    clearTimeout(tapResetTimer.value)
    requestAnimationFrame(() => {
      isTapped.value = true
      tapResetTimer.value = setTimeout(() => {
        isTapped.value = false
      }, 150)
    })

    // Accelerate if conditions met
    if (tapCount.value >= 10 && !showPopup.value) {
      tapCount.value = 0
      await onAccelerate()
      showPopup.value = true
      clearTimeout(popupTimer.value)
      popupTimer.value = setTimeout(() => {
        showPopup.value = false
      }, accelerateCooldown)
      timeFlash.value = true
      setTimeout(() => {
        timeFlash.value = false
      }, 350)
    }
  }

  return {
    tapCount,
    isTapped,
    showPopup,
    timeFlash,
    triggerTap,
  }
}
