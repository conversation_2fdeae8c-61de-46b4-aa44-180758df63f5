<script setup>
import { ref, computed, onMounted } from 'vue'
import { TitlePanelColor } from '@/components/common'
import  ShopLabel  from '@/features/iap/components/ShopLabel.vue'
import ShopPaymentPopup from '@/features/iap/components/ShopPaymentPopup.vue'
import { useShopStore } from '@/features/iap/stores/shopStore'
import { useRouter } from 'vue-router'


const router = useRouter()
const shopStore = useShopStore()
const selectedProduct = ref(null)
const showPurchaseModal = ref(false)

const specialOfferProducts = computed(() => {
  return shopStore.products.filter(product => product.type === 'special_offer').map(product => ({
    id: product.productId,
    createdOrderID: product.id,
    name: product.name,
    price: Math.round(product.priceKaia * 100 || 0) / 100,
    priceUsd: Math.round(product.priceUsd * 100) / 100,
    category: 'specialOffer',
    variant: (product.priceUsd || 0) > 10 ? 'gold' : 'default',
    canPurchase: product.canPurchase !== false,
    reason: product.reason || '',
    description: product.description || '',
    originalProduct: product
  }))
})

const timeWrapProducts = computed(() => {
  return shopStore.products.filter(product => product.type === 'time_warp').map(product => {
    
    return {
      id: product.productId,
      createdOrderID: product.id,
      name: product.name,
      price: Math.round(product.priceKaia * 100 || 0) / 100 ,
      priceUsd: Math.round(product.priceUsd * 100) / 100,
      category: 'timeWrap',
      variant: (product.priceUsd || 0) > 10 ? 'gold' : 'default',
      canPurchase: product.canPurchase !== false,
      reason: product.reason || '',
      description: product.description || '',
      originalProduct: product
    }
  })
})

const boosterProducts = computed(() => {
  return shopStore.products.filter(product => product.type === 'speed_boost').map(product => {
    const multiplier = product.multiplier || 2
    
    return {
      id: product.productId,
      createdOrderID: product.id,
      name: product.name,
      multiplier: `X${multiplier}`,
      price: Math.round(product.priceKaia * 100 || 0) / 100 ,
      priceUsd: Math.round(product.priceUsd * 100) / 100,
      category: 'booster',
      variant: (product.priceUsd || 0) > 15 ? 'gold' : 'default',
      canPurchase: product.canPurchase !== false,
      reason: product.reason || '',
      description: product.description || '',
      originalProduct: product
    }
  })
})


const closePurchaseModal = () => {
  if (shopStore.processing) {
    shopStore.cancelPayment()
  }
  
  showPurchaseModal.value = false
  selectedProduct.value = null
}

const purchaseProduct = (product) => {
  if (!product.canPurchase) {
    return
  }
  selectedProduct.value = product
  showPurchaseModal.value = true
}


const selectPaymentMethod = async (method) => {
  if (!selectedProduct.value || shopStore.processing) return
  
  try {
    const paymentData = {
      productId: selectedProduct.value.createdOrderID,
      imageUrl: `${window.location.origin}/iap/${selectedProduct.value.id}.png`,
      paymentMethod: method,
      // import.meta.env.PROD
      testMode: true //import.meta.env.DEV
    }
    
    const response = await shopStore.purchaseProduct(paymentData)
    console.log('Payment created:', response)
    
    closePurchaseModal()
    
    await shopStore.fetchProducts()
  } catch (error) {
    console.error('Purchase failed:', error)
  }
}

onMounted(() => {
  console.log('import.meta.env.PROD', import.meta.env.PROD)
  console.log('import.meta.env.DEV', import.meta.env.DEV)
})
</script>

<template>
  <div class="shop-container">
    

    <TitlePanelColor variant="green" class="shop-title">
      {{ $t('shop.title') }}
    </TitlePanelColor>

    <template v-if="specialOfferProducts.length > 0">
      <ShopLabel :text="$t('shop.categories.specialOffer')" />
      
      <div class="products-grid">
        <div 
          v-for="product in specialOfferProducts" 
          :key="product.id"
          :class="['product-card', 'time-wrap', product.variant, { 'disabled': !product.canPurchase }]"
          @click="purchaseProduct(product)"
        >
          <div class="product-icon">
              <img :src="`/iap/${product.id}.png`" alt="product icon" />
          </div>
          <div class="product-label">{{ product.name }}</div>
          <div class="product-price">
            <img src="/iap/moo-coin-icon.png" alt="coin" />
            <span>{{ product.price }}</span>
          </div>
        </div>
      </div>
    </template>
    
    <!-- 时间跳跃商品 -->
    <template v-if="timeWrapProducts.length > 0">
      <ShopLabel :text="$t('shop.categories.timeWrap')" />
      
      <div class="products-grid">
        <div 
          v-for="product in timeWrapProducts" 
          :key="product.id"
          :class="['product-card', 'time-wrap', product.variant, { 'disabled': !product.canPurchase }]"
          @click="purchaseProduct(product)"
        >
          <div class="product-icon">
              <img :src="`/iap/${product.id}.png`" alt="product icon" />
          </div>
          <div class="product-label">{{ product.name }}</div>
          <div class="product-price">
            <img src="/iap/moo-coin-icon.png" alt="coin" />
            <span>{{ product.price }}</span>
          </div>
        </div>
      </div>
    </template>

    <!-- 速度提升商品 -->
    <template v-if="boosterProducts.length > 0">
      <ShopLabel :text="$t('shop.categories.booster')" />
      <div class="products-grid">
        <div 
          v-for="product in boosterProducts" 
          :key="product.id"
          :class="['product-card', 'time-wrap', product.variant, { 'disabled': !product.canPurchase }]"
          @click="purchaseProduct(product)"
        >
          <div class="product-icon">
              <img :src="`/iap/${product.id}.png`" alt="product icon" />
          </div>
          <div class="product-label">{{ product.name }}</div>
          <div class="product-price">
            <img src="/iap/moo-coin-icon.png" alt="coin" />
            <span>{{ product.price }}</span>
          </div>
        </div>
      </div>
      <button 
        class="history-demo-button"
        @click="router.push('/v1/history')"
      >
        📋 {{ $t('history.title') }}
      </button>

    </template>

    <ShopPaymentPopup :isOpen="showPurchaseModal" @close="closePurchaseModal">
      <div class="payment-order-content" v-if="selectedProduct && !shopStore.processing">
        <div class="payment-title">{{ $t('common.paymentOrder') }}</div>
        <div class="payment-options">
          <div 
            :class="['payment-option', 'usd-option', { 'disabled': shopStore.processing }]" 
            @click="!shopStore.processing && selectPaymentMethod('usd')"
          >
            <div class="payment-icon">
              <img src="/iap/usd.png" alt="USD" />
            </div>
            <span>{{ selectedProduct.priceUsd }} USD</span>
          </div>
          
          <div 
            :class="['payment-option', 'kaia-option', { 'disabled': shopStore.processing }]" 
            @click="!shopStore.processing && selectPaymentMethod('kaia')"
          >
            <div class="payment-icon">
              <img src="/iap/kaia.png" alt="KAIA" />
            </div>
            <span>{{ selectedProduct.price }} KAIA</span>
          </div>
                  </div>
        
        <div class="payment-terms">
          <div class="term-item">
            1. You agree that the product(s)is/are non-refundable 
          </div>
          <div class="term-item">
            2. If paid via LINE IAP , you agree to providing encrypted ID info to LY Corporation.
          </div>
        </div>
      </div>
      
      <div class="payment-processing" v-if="selectedProduct && shopStore.processing">
        <div class="processing-content">
          <div class="spinner"></div>
          <div class="processing-text">
            <div class="payment-title">{{ $t('common.paymentOrder') }}</div>
            <span v-if="shopStore.paymentStep === 'creating'">{{ $t('shop.payment.creating') }}</span>
            <span v-else-if="shopStore.paymentStep === 'starting'">{{ $t('shop.payment.starting') }}</span>
            <span v-else-if="shopStore.paymentStep === 'completed'">{{ $t('shop.payment.completed') }}</span>
            <span v-else-if="shopStore.paymentStep === 'failed'">{{ $t('shop.payment.failed') }}</span>
            <span v-else>{{ $t('shop.payment.processing') }}</span>
          </div>
        </div>
      </div>
      
    </ShopPaymentPopup>
  </div>
</template>


<style scoped>
.shop-container {
  margin: calc(var(--base-unit) * 10) calc(var(--base-unit) * 20);
  margin-bottom: calc(var(--base-unit) * 20);
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 24);
}

.shop-title {
  margin-bottom: calc(var(--base-unit) * 8);
}

.vip-demo-section {
  display: flex;
  gap: calc(var(--base-unit) * 12);
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: calc(var(--base-unit) * 24);
}

.vip-demo-button {
  background: linear-gradient(45deg, #8FCD2D, #6BA32A);
  color: white;
  border: calc(var(--base-unit) * 2) solid #FDD99B;
  border-radius: calc(var(--base-unit) * 8);
  padding: calc(var(--base-unit) * 10) calc(var(--base-unit) * 16);
  font-size: calc(var(--base-unit) * 14);
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s ease;
  text-align: center;
}

.history-demo-button {
  background: linear-gradient(45deg, #2D7CCD, #1E5A9C);
  color: white;
  border: calc(var(--base-unit) * 2) solid #FDD99B;
  border-radius: calc(var(--base-unit) * 8);
  padding: calc(var(--base-unit) * 10) calc(var(--base-unit) * 16);
  font-size: calc(var(--base-unit) * 14);
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s ease;
  text-align: center;
}

.vip-demo-button:hover {
  transform: scale(1.02);
}

.history-demo-button:hover {
  transform: scale(1.02);
}

.vip-demo-button:active {
  transform: scale(0.98);
}

.history-demo-button:active {
  transform: scale(0.98);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: calc(var(--base-unit) * 16);
  justify-content: center;
  max-width: calc(var(--base-unit) * 400);
  margin: 0 auto;
}

.product-card.time-wrap {
  background-color: #CF8E61;
  border-radius: calc(var(--base-unit) * 10);
  padding: calc(var(--base-unit) * 10);
  text-align: center;
  box-shadow: none;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(var(--base-unit) * 130);
  position: relative;
  border: 4px solid #FDD99B;
  margin-bottom: calc(var(--base-unit) * 30);
}

.product-card.time-wrap.gold {
  background: linear-gradient(to bottom, #F59A13, #A74B8D);
}

.product-card:active {
  transform: scale(0.97);
}

.product-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.product-card.disabled:active {
  transform: none;
}

.product-icon {
  margin-bottom: calc(var(--base-unit) * 8);
  display: flex;
  justify-content: center;
}

.product-icon img {
  width: calc(var(--base-unit) * 60);
  height: calc(var(--base-unit) * 60);
  object-fit: contain;
  max-width: none;
}

.product-card.time-wrap .product-label {
  font-size: calc(var(--base-unit) * 12);
  font-weight: 700;
  color: #FCF1CB;
  margin-bottom: calc(var(--base-unit) * 8);
  line-height: 1.3;
}

.product-card.time-wrap .product-price {
  background: #FDC844;
  color: white;
  font-weight: bold;
  padding: calc(var(--base-unit) * 2) calc(var(--base-unit) * 8);
  border-radius: calc(var(--base-unit) * 10);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: calc(var(--base-unit) * 6);
  border: calc(var(--base-unit) * 3) solid #FF8A28;
  box-shadow: 0 0 0 calc(var(--base-unit) * 2) black;
  width: 90%;
  margin-top: calc(var(--base-unit) * 20);
  margin-bottom: calc(var(--base-unit) * -48);
  align-items: flex-end
}

.product-card.time-wrap .product-price img {
  width: calc(var(--base-unit) * 20);
  height: calc(var(--base-unit) * 20);
  object-fit: contain;
  flex-shrink: 0;
  display: block;
  max-width: none;
}

.product-card.time-wrap .product-price span {
  font-size: calc(var(--base-unit) * 14);
  -webkit-text-stroke-width: calc(var(--base-unit) * 2);
  -webkit-text-stroke-color: #3B3B3B;
}

.loading-spinner {
  text-align: center;
  padding: calc(var(--base-unit) * 32);
  color: rgba(75, 89, 116, 1);
}

.spinner {
  display: inline-block;
  width: calc(var(--base-unit) * 40);
  height: calc(var(--base-unit) * 40);
  border: calc(var(--base-unit) * 4) solid rgba(161, 193, 230, 0.3);
  border-top: calc(var(--base-unit) * 4) solid rgba(45, 114, 193, 1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: calc(var(--base-unit) * 16);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}











.payment-order-content {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 16);
  text-align: center;
}

.payment-title {
  font-size: calc(var(--base-unit) * 18);
  font-weight: bold;
  color: #6C2C0F;
  text-align: center;
  margin-bottom: calc(var(--base-unit) * 16);
  padding-bottom: calc(var(--base-unit) * 8);
  border-bottom: 2px solid #6C2C0F;
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 12);
  margin-bottom: calc(var(--base-unit) * 16);
}

.payment-option {
  background: #FDC844;
  border-radius: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 12) calc(var(--base-unit) * 16);
  border: calc(var(--base-unit) * 3) solid #FF8A28;
  box-shadow: 0 0 0 calc(var(--base-unit) * 3) #252635;
  display: flex;
  align-items: center;
  gap: calc(var(--base-unit) * 8);
  cursor: pointer;
  transition: transform 0.2s ease;
  justify-content: center;
  align-items: center;
}

.payment-option:hover {
  transform: scale(1.02);
}

.payment-option:active {
  transform: scale(0.98);
}

.payment-option.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.payment-option.disabled:hover {
  transform: none;
}

.payment-option.disabled:active {
  transform: none;
}

.payment-icon {
  height: calc(var(--base-unit) * 32);
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-icon img {
  height: calc(var(--base-unit) * 32);
  object-fit: contain;
}

.payment-option span {
  font-size: calc(var(--base-unit) * 18);
  font-weight: bold;
  color: #ffffff;
  -webkit-text-stroke-width: calc(var(--base-unit) * 2);
  -webkit-text-stroke-color: #3B3B3B;
  line-height: 0.2;
  display: flex;
  align-items: center;
  margin-top: calc(var(--base-unit) * 6);
}



.payment-terms {
  background: #B56A28;
  border-radius: calc(var(--base-unit) * 8);
  padding: calc(var(--base-unit) * 12);
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 8);
  text-align: left;
  border: 2px solid #6C2C0F;
}

.term-item {
  font-size: calc(var(--base-unit) * 11);
  color: #FCF1CB;
  line-height: 1.4;
}

.payment-processing {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: calc(var(--base-unit) * 40);
}

.processing-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: calc(var(--base-unit) * 16);
}

.processing-text {
  font-size: calc(var(--base-unit) * 16);
  color: #6C2C0F;
  font-weight: bold;
}
</style> 