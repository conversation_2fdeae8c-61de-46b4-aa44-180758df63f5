import { Assets } from 'pixi.js';
import { GameObject, SpriteRendererComponent, Scene, Vector2 } from '@/features/gameplay/shared';

export class ConveyorGameObject extends GameObject {
  private spriteRenderer!: SpriteRendererComponent;

  constructor(scene: Scene) {
    super(scene);

    // Set layout via transform (relative to screen)
    this.transform.scale = new Vector2(1, 1);
    this.transform.position = new Vector2(0.5, 0.85);
  }

  async start(): Promise<void> {
    const texture = await Assets.load('/assets/deliveryLine/conveyor.png');

    this.spriteRenderer = new SpriteRendererComponent({
      texture,
      anchor: { x: 0.5, y: 0.5 },
    });

    this.addComponent(this.spriteRenderer);
  }

  protected onUpdate(deltaSeconds: number): void {
    // Optional: animate conveyor belt
  }

  get height(): number {
    return this.spriteRenderer.sprite.height;
  }
}
