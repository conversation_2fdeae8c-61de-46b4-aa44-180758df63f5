<template>
    <div class="reward-container">
      <img class="plus-icon" src="/icon/plus.png">
      <span class="count-text main-text">{{ amount }}</span>
      <img class="chest-icon" src="/icon/chest.png">
    </div>
  </template>
  
  <script>
  export default {
    name: 'PlusChestAmount',
    props: {
      amount: {
        type: [Number, String],
        default: 1
      }
    }
  }
  </script>
  
  <style scoped>
  .reward-container {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    gap: calc(var(--base-unit) * 8);
  }
  
  .plus-icon {
    width: calc(var(--base-unit) * 16);
    height: 100%;
    object-fit: contain;
  }
  
  .count-text {
    font-size: calc(var(--base-unit) * 16);
  }
  
  .chest-icon {
    width: calc(var(--base-unit) * 32);
    height: calc(var(--base-unit) * 32);
    object-fit: contain;
  }
  </style>