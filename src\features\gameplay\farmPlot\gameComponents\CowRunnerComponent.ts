import { GameComponent, Vector2, Mathf, GameObject } from '@/features/gameplay/shared';
import { CowBoostComponent } from './CowBoostComponent';

export class CowRunnerComponent extends GameComponent {
  private target: Vector2 = new Vector2(0.5, 0.5);
  private speed = 0.04;
  private threshold = 0.01;
  private boostComponent: CowBoostComponent | null = null;

  override attach(gameObject: GameObject): void {
    super.attach(gameObject);
    this.pickNewTarget();
    
    // Get reference to boost component
    this.boostComponent = gameObject.getComponent(CowBoostComponent);
  }

  override update(deltaSeconds: number): void {
    const current = this.gameObject.transform.position;

    const dx = this.target.x - current.x;
    const dy = this.target.y - current.y;
    const distSq = dx * dx + dy * dy;
    if (distSq < this.threshold * this.threshold) {
      this.pickNewTarget();
      return;
    }
    const distance = Math.sqrt(distSq);
    const dirX = dx / distance;
    const dirY = dy / distance;
    

    // Apply boost multiplier to speed if boosted
    const effectiveSpeed = this.boostComponent?.getBoostState() 
      ? this.speed * 4 // 4x speed when boosted
      : this.speed;

    const moveX = dirX * effectiveSpeed * deltaSeconds;
    const moveY = dirY * effectiveSpeed * deltaSeconds;

    const pos = this.gameObject.transform.position;
    pos.x += moveX;
    pos.y += moveY;    

    
    const scale = this.gameObject.transform.scale;
    const currentSign = Math.sign(scale.x);
    const newSign = dirX > 0 ? -1 : 1;
    if (currentSign !== newSign) {
      scale.x = newSign * Math.abs(scale.x);
    }
  }

  private pickNewTarget() {
    const x = Math.random() * 0.9 - 0.45;
    const y = Math.random() * 0.25 + 0.1; // stays in lower portion
    this.target = new Vector2(x, y);
  }

  randomizePosition() {
    const x = Math.random() * 0.9 - 0.45;
    const y = Math.random() * 0.25 + 0.1;
    this.gameObject.transform.position = new Vector2(x, y);
  }
}
