<script setup lang="ts">
import { useReferralCode } from '../composables/useReferralCode'
import { useI18n } from 'vue-i18n'
import { usePlatform } from '@/composables/usePlatform'
import { generateReferralLink } from '@/utils/config'

const { referralCode, copyToClipboard } = useReferralCode()
const { t } = useI18n()
const { isInLIFF } = usePlatform()

const shareReferralCode = async () => {
  // Check if we're in LIFF and shareTargetPicker is available
  const liff = (window as any).liff
  if (isInLIFF.value && liff && liff.isApiAvailable("shareTargetPicker")) {
    try {
      const link = generateReferralLink(referralCode.value)
      if (link) {
        await liff.shareTargetPicker([
          {
            type: "text",
            text: link,
          },
        ])
      }
    } catch (error) {
      console.error('Failed to share via LIFF:', error)
      // Fall back to copy to clipboard if sharing fails
      copyToClipboard()
    }
  } else {
    // Fall back to copy to clipboard if not in LIFF or shareTargetPicker not available
    copyToClipboard()
  }
}
</script>

<template>
  <div class="referral-code-container">
    <button class="referral-code" @click="shareReferralCode">
      {{ referralCode || t('referral.defaultCodePlaceholder') }}
    </button>
    <button class="copy-button main-text" @click="copyToClipboard">
      {{ t('button.copy') }}
    </button>
  </div>
</template>

<style scoped>
.referral-code-container {
  display: flex;
  align-items: center;
}

.referral-code {
  flex: 1;
  padding: calc(var(--base-unit) * 14);
  background-color: rgba(230, 235, 247, 0.5);
  border: calc(var(--base-unit) * 2) solid rgba(139, 149, 179, 1);
  border-radius: calc(var(--base-unit) * 12);
  font-size: calc(var(--base-unit) * 14);
  color: rgba(75, 89, 116, 1);
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10px;
  transition: all 0.2s;
}

.copy-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: calc(var(--base-unit) * 14) calc(var(--base-unit) * 16);
  background-color: rgba(10, 189, 93, 1);
  border: calc(var(--base-unit) * 4) solid rgba(37, 38, 53, 1);
  border-radius: calc(var(--base-unit) * 12);
  font-size: calc(var(--base-unit) * 14);
  transition: all 0.2s;
}

.copy-button:active,
.referral-code:active {
  transform: scale(0.95);
}
</style>
