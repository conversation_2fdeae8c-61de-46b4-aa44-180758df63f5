<script setup>
import { ColorButton, AppInput, AppPopup, TitlePanelColor } from '@/components/common'
import { usePlayerPopupStore } from '../stores/playerPopupStore'
import { useUserInfo } from '@/features/userInfo'

import { updateUsername } from '../api'
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const { userInfo, fetchUserInfo } = useUserInfo()
const playerPopupStore = usePlayerPopupStore()

const name = ref(userInfo?.username || '')

const onConfirm = async () => {
  if (!name.value || name.value === userInfo.value?.username) return
  await updateUsername(name.value)
  await fetchUserInfo()
  playerPopupStore.closePlayerPopup()
}


</script>

<template>
  <AppPopup :isOpen="playerPopupStore.showPlayerPopup" @close="playerPopupStore.closePlayerPopup">
    <div class="player-popup">
      <TitlePanelColor class="title">{{ $t('common.editUsername') }}</TitlePanelColor>
      <AppInput class="input" :placeholder="userInfo?.username || $t('common.enterName')" v-model="name"/>
      <ColorButton class="button" @click="onConfirm">{{ $t('common.confirm') }}</ColorButton>
    </div>
  </AppPopup>
</template>

<style scoped>
.player-popup {
  color: white;
  text-align: center;
}

.title {
  font-size: calc(var(--base-unit) * 10);
  margin-bottom: calc(var(--base-unit) * 18);
}

.input {
  margin-bottom: calc(var(--base-unit) * 32);
  text-align: center;
}

.button {
  width: 100%;
}
</style>
