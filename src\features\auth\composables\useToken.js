import { ref } from 'vue'

const token = ref(null)

export const useToken = () => {
  const setToken = (newToken) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  const loadToken = () => {
    token.value = localStorage.getItem('token')
    console.log("token: ", token.value );
  }

  const clearToken = () => {
    token.value = null
    localStorage.removeItem('token')
  }

  const isTokenExpired = (jwt) => {
    if (!jwt) return true
    try {
      const payload = JSON.parse(atob(jwt.split('.')[1]))
      const now = Math.floor(Date.now() / 1000)
      console.log("payload:", payload);
      return payload.exp && payload.exp < now
    } catch {
      return true
    }
  }

  return {
    token,
    setToken,
    loadToken,
    clearToken,
    isTokenExpired: () => isTokenExpired(token.value),
  }
}
