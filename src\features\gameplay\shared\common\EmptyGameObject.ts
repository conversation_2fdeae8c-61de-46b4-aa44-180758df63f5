import { GameObject } from '../core/GameObject';
import { Scene } from '../scene/Scene';

export class EmptyGameObject extends GameObject {
  constructor(scene: Scene, id?: string, tag?: string) {
    super(scene, id, tag);
  }

  override async start(): Promise<void> {
    // Nothing visual, but children can be added and resized
    this.transform.resize();
  }

  protected override onUpdate(_deltaSeconds: number): void {
    // Could forward update if needed
  }
}
