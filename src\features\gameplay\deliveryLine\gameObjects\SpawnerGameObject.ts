import { Application, Container, Assets } from 'pixi.js';
import { GameObject, SpriteRendererComponent, Scene, Vector2 } from '@/features/gameplay/shared';

export class SpawnerGameObject extends GameObject {
  private spriteRenderer!: SpriteRendererComponent;

  constructor(scene: Scene) {
    super(scene);

    // Move layout configuration to the transform
    this.transform.scale = new Vector2(0.15, 0.15);
    this.transform.position = new Vector2(0, 0.35); // 35% from top
  }

  async start(): Promise<void> {
    const texture = await Assets.load('/assets/deliveryLine/milk-produce.png');

    this.spriteRenderer = new SpriteRendererComponent({
      texture,
      anchor: { x: 0, y: 0.5 }, // Bottom-left edge aligned
    });

    this.addComponent(this.spriteRenderer);
    this.spriteRenderer.render(); // Apply layout
  }

  protected onUpdate(deltaSeconds: number): void {
    // Optional: Add animation or behavior later
  }
}
