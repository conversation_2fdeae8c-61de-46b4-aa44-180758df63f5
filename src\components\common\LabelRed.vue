<template>
    <div class="label-container">
      <span class="label">
        <slot></slot>
      </span>
    </div>
  </template>
  
  <script>
  import { defineComponent } from 'vue';
  
  export default defineComponent({
    name: 'LabelRed'
  });
  </script>
  
  <style scoped>
  .label-container {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #E74C3C;
    height: calc(var(--base-unit) * 24);
    padding: calc(var(--base-unit) * 4) calc(var(--base-unit) * 12);
    transform: skew(-10deg);
    border: calc(var(--base-unit) * 2) solid black;
    border-radius: calc(var(--base-unit) * 4);
    box-sizing: border-box;
  }
  
  .label {
    color: white;
    font-size: calc(var(--base-unit) * 10);
    font-weight: bold;
    transform: skew(10deg);
    white-space: nowrap;
    text-shadow: none;
    -webkit-text-stroke: 0;
  }
  </style>