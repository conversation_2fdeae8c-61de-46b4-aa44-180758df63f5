<script setup>
import { AppRow, RoundedLabel, PlusChestAmount } from '@/components/common'

defineProps({
  task: Object,
  rewardAmount: Number
})
</script>

<template>
  <AppRow
    @click="$emit('click')"
    class="task-panel"
    :class="{
      'task-panel-disabled': !task.canComplete && !task.isCompleted,
      'task-panel-completed': task.isCompleted,
      'task-panel-available': task.canComplete && !task.isCompleted
    }"
  >
    <span>{{ task.name }}</span>
    <template #right>
      <div v-if="task.isCompleted">
        <RoundedLabel>{{ $t('earn.completed') }}</RoundedLabel>
      </div>
      <PlusChestAmount :amount="rewardAmount" v-else-if="task.canComplete" />
      <PlusChestAmount
        v-else
        :amount="rewardAmount"
        :class="{ 'disabled-reward': !task.canComplete }"
      />
    </template>
  </AppRow>
</template>
