// stores/jackpotStore.js
import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useJackpotStore = defineStore('jackpot', () => {
  const isVisible = ref(false);
  const rewardData = ref(null);

  function showJackpot(data) {
    rewardData.value = data;
    isVisible.value = true;
  }

  function closeJackpot() {
    isVisible.value = false;
    rewardData.value = null;
  }

  return {
    isVisible,
    rewardData,
    showJackpot,
    closeJackpot
  };
});
