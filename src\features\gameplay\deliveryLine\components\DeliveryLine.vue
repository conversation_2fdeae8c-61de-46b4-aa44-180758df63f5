<template>
  <div class="delivery-line" ref="containerRef">
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useDeliveryLineScene } from '../scenes/useDeliveryLineScene';

const containerRef = ref<HTMLElement | null>(null);

onMounted(async () => {
  await useDeliveryLineScene(containerRef.value);
});

</script>


<style scoped>
.delivery-line {
  width: 100%;
  height: calc(var(--base-unit) * 120);
  border: 2px solid #555;
  background-color: #151515;
}
</style>
