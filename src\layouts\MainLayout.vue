<script setup>
import TopSection from '@/components/TopSection.vue';
import { Background } from '@/components/common';
import { GameMenu, gameMenuItems } from '@/features/gameMenu'
import GameNotification from '@/components/vue/GameNotification.vue';
import { OpenChestOverlay } from '@/features/openChest';
import { PlayerPopup } from '@/features/userInfo';
import { VipContent } from '@/features/vip';

</script>

<!-- MainLayout.vue -->
<template>
  <Background :imagePath="'/img/menu-background.svg'" :style="'backgroundColor:rgba(44, 55, 87, 1)'"/>
  <GameNotification />
  <TopSection/>
  <div class="section-container">
    <router-view />  <!-- Page content changes here -->
  </div>
  <GameMenu ref="gameMenu"/>
  <OpenChestOverlay ref="chestOverlay"/>
  <PlayerPopup/>
  <VipContent/>
  
</template>

<style scoped>
.section-container {
  position: fixed;
  width: 100%;
  display: flex;
  flex-direction: column;
  height: calc(100% - calc(var(--base-unit) * (124 + 36))); /* Adjust height dynamically */
  
  overflow-y: scroll; /* Make only the content inside scroll */
  overflow-x: hidden;
  top: calc(var(--base-unit) * 84);
  bottom: calc(var(--base-unit) * 76);
  
  /* Hide scrollbar for all browsers */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.section-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}
</style>
