import { GameObject } from '../core/GameObject';
import { Vector2 } from '../generic/Vector2';
import { Mathf } from '../generic/Mathf';
import { TweenBuilder, Easing } from '@/features/gameplay/animation';
import { Scene } from '../scene/Scene';
import { TextObject } from './TextObject'; // Adjust path as needed

export interface PopTextConfig {
  text: string;
  scale?: Vector2;
  position: Vector2;
  style?: Partial<TextObject['text']['style']>;
  duration?: number;
  scaleMultiplier?: number;
  moveDistance?: number;
}

export class PopTextGameObject extends GameObject {
  public textObject!: TextObject;
  private config: Required<PopTextConfig>;
  private isFinished = false;

  constructor(scene: Scene, config: PopTextConfig) {
    super(scene);

    this.config = {
      text: config.text,
      scale: config.scale ?? new Vector2(1, 1),
      position: config.position,
      style: config.style ?? {},
      duration: config.duration ?? 1,
      scaleMultiplier: config.scaleMultiplier ?? 1.2,
      moveDistance: config.moveDistance ?? 1
    };
  }

  override async start(): Promise<void> {
    this.transform.position = this.config.position.clone();
    this.transform.scale = this.config.scale.clone();
    
    // Create and start the text label
    this.textObject = new TextObject(this.scene, {
      text: this.config.text,
      style: this.config.style,
      scale: this.transform.scale.clone(),
      position: this.transform.position.clone()
    });
    GameObject.instantiate(this.textObject);

    this.textObject.transform.scale = new Vector2(0,0);

    // Scale up
    TweenBuilder
      .update(this.textObject.transform.scale, (target, value) => {
        target.x = Mathf.Lerp(0, this.config.scale.x * this.config.scaleMultiplier, value);
        target.y = Mathf.Lerp(0, this.config.scale.y * this.config.scaleMultiplier, value);
      }, 0, 1, this.config.duration * 0.3)
      .setEase(Easing.EaseOutCubic)
      .play();

    // Scale down
    TweenBuilder
      .update(this.textObject.transform.scale, (target, value) => {
        target.x = Mathf.Lerp(this.config.scale.x * this.config.scaleMultiplier, this.config.scale.x, value);
        target.y = Mathf.Lerp(this.config.scale.y * this.config.scaleMultiplier, this.config.scale.y, value);
      }, 0, 1, this.config.duration * 0.3)
      .setDelay(this.config.duration * 0.3)
      .setEase(Easing.EaseInOutQuad)
      .play();

    // Move upward
    TweenBuilder
      .update(this.transform.position, (target, value) => {
        target.y = this.config.position.y - value;
        this.textObject.transform.position.y = target.y;
      }, 0, this.config.moveDistance, this.config.duration)
      .setEase(Easing.EaseOutCubic)
      .play();

    // Fade out and destroy
    TweenBuilder
      .update(this.textObject.text, (target, value) => {
        target.alpha = 1 - value;
      }, 0, 1, this.config.duration * 0.3)
      .setDelay(this.config.duration * 0.7)
      .setEase(Easing.Linear)
      .setComplete(() => {
        if (!this.isFinished) {
          this.isFinished = true;
          this.destroy();
        }
      })
      .play();
  }

  protected override onUpdate(deltaSeconds: number): void {
    // No-op, tweens manage the animation
  }

  override destroy(): void {
    if (this.isDestroyed) return;
    this.textObject?.destroy();
    super.destroy();
  }
  
}
