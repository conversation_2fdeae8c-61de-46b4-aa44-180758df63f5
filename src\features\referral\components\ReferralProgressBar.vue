<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  value: number
  milestones: number[]
  max: number
}>()

// Ensure capped value
const cappedValue = computed(() => Math.min(props.value, props.max))

// Find the next milestone user hasn’t reached yet
const nextMilestone = computed(() =>
  props.milestones.find((m) => props.value < m)
)

// Compute the progress bar width using segmented distribution
const progressWidth = computed(() => {
  const segments = [
    { min: 0, max: 2, start: 0, end: 5 },
    { min: 3, max: 5, start: 18, end: 24 },
    { min: 6, max: 10, start: 38, end: 43 },
    { min: 11, max: 20, start: 58, end: 62 },
    { min: 21, max: 29, start: 77, end: 81 },
    { min: 30, max: 30, start: 100, end: 100 }
  ]

  const current = segments.find(s => cappedValue.value >= s.min && cappedValue.value <= s.max)
  if (!current) return 0

  const range = current.max - current.min
  const progress = range ? (cappedValue.value - current.min) / range : 1
  return current.start + (progress * (current.end - current.start))
})
</script>

<template>
  <div class="progress-section">
    <div class="progress-bar">
      <div class="progress-indicator" :style="{ width: `${progressWidth}%` }"></div>
    </div>

    <div class="chest-column"></div>

    <div
      class="chest-column"
      v-for="milestone in milestones"
      :key="milestone"
    >
      <div
        class="chest-box"
        :class="{
          'chest-box-disabled': value < milestone,
          'chest-box-pulsing': value < milestone && milestone === nextMilestone
        }"
      >
        <slot name="icon" :milestone="milestone">
          <img src="/icon/chest.png" alt="milestone" />
        </slot>
      </div>
      <div class="user-count">
        <slot name="label" :milestone="milestone">
          {{ milestone }}
        </slot>
      </div>
    </div>

    <div class="chest-column"></div>
  </div>
</template>

<style scoped>
.progress-section {
  position: relative;
  display: flex;
  justify-content: space-between;
}
.progress-bar {
  position: absolute;
  top: calc(var(--base-unit) * 23);
  left: 0;
  right: 0;
  height: calc(var(--base-unit) * 6);
  border-radius: calc(var(--base-unit) * 4);
  background-color: rgba(217, 217, 217, 1);
  z-index: 0;
  overflow: hidden;
}
.progress-indicator {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #FFD700;
  border-radius: calc(var(--base-unit) * 4);
  transition: width 0.5s ease;
}
.chest-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: calc(var(--base-unit) * 8);
  z-index: 1;
}
.user-count {
  font-size: calc(var(--base-unit) * 8);
  color: white;
  font-weight: bold;
}
.chest-box {
  width: calc(var(--base-unit) * 46);
  height: calc(var(--base-unit) * 46);
  background-image: linear-gradient(to bottom, rgba(177, 58, 255, 1), rgba(106, 35, 153, 1));
  border-radius: calc(var(--base-unit) * 8);
  box-shadow: inset 0 0 0 calc(var(--base-unit) * 1) rgba(0, 0, 0, 1);
  display: flex;
  justify-content: center;
  align-items: center;
}
.chest-box-disabled {
  filter: grayscale(100%) brightness(0.8);
  background-color: rgba(88, 89, 91, 1);
  background-image: none;
}
.chest-box-pulsing {
  animation: pulseScale 1s infinite ease-in-out;
}
.chest-box img {
  width: calc(var(--base-unit) * 40);
  height: calc(var(--base-unit) * 40);
}
@keyframes pulseScale {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}
</style>
