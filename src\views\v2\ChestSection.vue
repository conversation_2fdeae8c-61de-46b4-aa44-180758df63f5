
<template>
    <!--
    <div class="chest-container">
        <InfoPanel>
            <TitlePanelColor :variant="'green'">
                {{ t('common.luckyDraw') }}
            </TitlePanelColor>
            <img class='chest-img' src='/icon/chest.png'>
            <div class="resources-container">
                <ResourceCard/>
                <ResourceCard 
                :title="t('common.ticket')"
                imageSrc="/icon/ticket.png"
                bgColor="linear-gradient(-82deg, 
                    #71C983 0%,
                    #86EA9A 52%,
                    #5DE378 61%,
                    #8DEBA0 72%,
                    #337D42 100%
                )"
                borderColor="#000000"
                contentBg="#BCCFDF"
                iconBg="#999EA4"
                iconBorderColor="#728EA2"
                />
                <ResourceCard
                :title="t('common.ton')"
                imageSrc="/icon/toncoin.webp"
                bgColor="#CDE8F4"
                borderColor="#000000"
                contentBg="#3CA4D8"
                iconBg="#CDE8F4"
                iconBorderColor="#048BCC"
                />
            </div>
        </InfoPanel>
        <HighlightButton :text="t('common.draw1Times')"/>
    </div>
    -->
</template>
<!--
<script setup>
import { useI18n } from 'vue-i18n'
import TitlePanelColor from '@/components/common/TitlePanelColor.vue'
import HighlightButton from '@/components/ui/HighlightButton.vue';
import ResourceCard from '@/components/vue/ResourceCard.vue';
import InfoPanel from '@/components/ui/InfoPanel.vue'

const { t } = useI18n()
</script>

<style scoped>
.chest-container {
    margin:  calc(var(--base-unit) * 10) calc(var(--base-unit) * 20);
    margin-bottom: calc(var(--base-unit) * 20);
    display: flex;
    flex-direction: column;
    gap: calc(var(--base-unit) * 16);
}


.chest-img {
    width: calc(var(--base-unit) * 137);
    height: calc(var(--base-unit) * 140);
    object-fit: contain; /* Ensures the image scales properly without distortion */
    align-self: center; /* Centers the image inside the flex container */
    margin-right: calc(var(--base-unit) * 8); /* Adjust this value as needed */
}

.resources-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: calc(var(--base-unit) * 16);
}

</style>
-->