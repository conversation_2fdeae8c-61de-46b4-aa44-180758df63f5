// TweenManager.ts
import { Ticker } from 'pixi.js';
import { Tween, TweenConfig } from './Tween';

export class TweenManager {
  private static instance: TweenManager;
  private tweens: Tween<any>[] = [];
  private isRunning = false;

  private constructor() {
    // No immediate ticker registration—wait for tweens
  }

  static getInstance(): TweenManager {
    if (!TweenManager.instance) {
      TweenManager.instance = new TweenManager();
    }
    return TweenManager.instance;
  }

  /**
   * Main update loop for Pixi.js Ticker.
   * Accepts either a Ticker object or a number in seconds for manual calls.
   */
  update = (delta: number | Ticker): void => {
    const deltaSeconds =
      typeof delta === 'number' ? delta : delta.deltaMS / 1000;

    if (this.tweens.length === 0) {
      this.stopUpdating();
      return;
    }

    this.tweens = this.tweens.filter((tween) => {
      tween.update(deltaSeconds);
      return !tween.completed;
    });

    if (this.tweens.length === 0) {
      this.stopUpdating();
    }
  };

  /**
   * Starts updating the tween manager using <PERSON><PERSON>'s shared ticker.
   */
  private startUpdating(): void {
    if (this.isRunning) return;
    this.isRunning = true;
    Ticker.shared.add(this.update, this);
  }

  /**
   * Stops updating when no tweens remain.
   */
  private stopUpdating(): void {
    if (!this.isRunning) return;
    this.isRunning = false;
    Ticker.shared.remove(this.update, this);
  }

  /**
   * Create and register a new tween.
   */
  create<T>(config: TweenConfig<T>): Tween<T> {
    const tween = new Tween(config);
    this.tweens.push(tween);

    if (!this.isRunning) {
      this.startUpdating();
    }

    return tween;
  }

  /**
   * Removes all active tweens associated with a specific target.
   */
  removeTweensOf(target: any): void {
    const before = this.tweens.length;
    this.tweens = this.tweens.filter(
      (tween) => tween.getTarget() !== target
    );

    if (before > 0 && this.tweens.length === 0) {
      this.stopUpdating();
    }
  }

  /**
   * Stops and removes all tweens.
   */
  clear(): void {
    this.tweens = [];
    this.stopUpdating();
  }
}
