import { Vector2 } from '../generic/Vector2';
import { GameObject } from './GameObject';

export class Transform {
  private _position: Vector2 = new Vector2(0.5, 0.5);
  get position(): Vector2 {
    return this._position;
  }
  set position(value: Vector2) {
    this._position = value;
    this.onChanged?.();
  }

  private _scale: Vector2 = new Vector2(1, 1);
  get scale(): Vector2 {
    return this._scale;
  }
  set scale(value: Vector2) {
    this._scale = value;
    this.onChanged?.();
  }

  private _rotation: number = 0;
  get rotation(): number {
    return this._rotation;
  }
  set rotation(value: number) {
    this._rotation = value;
    this.onChanged?.();
  }


  public parent: Transform | null = null;
  public children: Transform[] = [];

  public onChanged?: () => void;

  constructor(public readonly gameObject: GameObject) {}

  addChild(child: Transform): void {
    child.parent = this;
    this.children.push(child);
  }

  removeChild(child: Transform): void {
    const i = this.children.indexOf(child);
    if (i !== -1) {
      child.parent = null;
      this.children.splice(i, 1);
    }
  }

  resize(): void {
    this.onChanged?.();

    for (const child of this.children) {
      child.resize();
    }
  }
}
