import { GameComponent, GameObject } from '@/features/gameplay/shared';
import { CowGameObject } from '../gameObjects/CowGameObject';
import type { FarmPlotStats } from '../stores/farmPlotStore';
import { Ref } from 'vue';

export class FarmPlotRunnerComponent extends GameComponent {
  private spawnedCows: CowGameObject[] = [];

  constructor(private readonly plot: Ref<FarmPlotStats>) {
    super();
  }

  override attach(gameObject: import('@/features/gameplay/shared').GameObject): void {
    super.attach(gameObject);
    this.syncCows();
  }

  override update(deltaSeconds: number): void {
    if(!this.plot.value.isUnlocked) this.plot.value.barnCount = 0;
    const desiredBarnCount = this.plot.value.barnCount;
    if (this.spawnedCows.length !== desiredBarnCount) {
      this.syncCows();
    }
  }

  private async syncCows(): Promise<void> {
    // Destroy existing cows
    for (const cow of this.spawnedCows) {
      cow.destroy(); 
    }
    this.spawnedCows = [];

    // Re-spawn cows
    const scene = this.gameObject.scene;
    for (let i = 0; i < this.plot.value.barnCount; i++) {
      const cow = await GameObject.instantiate(new CowGameObject(scene, this.plot));

      cow.transform.parent = this.gameObject.transform;
      this.spawnedCows.push(cow);

    }
  }

  override destroy(): void {
    for (const cow of this.spawnedCows) {
      cow.destroy(); // ✅ Clean removal from scene
    }
    this.spawnedCows = [];
  }
}
