<script setup>
import { useI18n } from 'vue-i18n';
import audioService from '@/lib/audioService'
import { useRouter } from 'vue-router'

const { t } = useI18n();
const router = useRouter()

const selectChestSection = () => {
    audioService.play('button1'); // Play sound effect
    router.push('v1/inventory')
};
</script>

<template>
    <div class="chest-button-container">
        <button :class="['rounded-button']" @click="selectChestSection">
            <div class="icon"></div>
            <span class="button-label">{{ t('buttons.items') }}</span>
        </button>
    </div>
</template>

<style scoped>
.chest-button-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: calc(var(--base-unit) * 64);
    position: relative;
}

.rounded-button {
    width: calc(var(--base-unit) * 64);
    height: calc(var(--base-unit) * 48);
    background: rgba(69, 67, 89, 1);
    border: none;
    border-radius: calc(var(--base-unit) * 12) 0px 0px calc(var(--base-unit) * 12);
    box-shadow: 0px calc(var(--base-unit) * 5) calc(var(--base-unit) * 1) rgba(8, 3, 10, 1);
    padding: 0;
    margin: 0;
}

.icon {
    position: absolute;
    top: calc(var(--base-unit) * -43);
    left: 50%;
    transform: translateX(-50%);
    width: calc(var(--base-unit) * 64);
    height: calc(var(--base-unit) * 64);
    background: url('/icon/green-fragment.png') center/cover no-repeat;
    filter: drop-shadow(0 calc(var(--base-unit) * 4) calc(var(--base-unit) * 4) rgba(0, 0, 0, 0.33));
    transition: transform 0.15s ease-out;
}

.rounded-button:active .icon {
    transform: translateX(-50%) translateY(-10%);
}

.button-label {
    color: white;
    font-size: calc(var(--base-unit) * 14);
    display: inline-block;
    transition: transform 0.15s ease-out;
}

.rounded-button:active .button-label {
    transform: scale(1.1);
}
</style>