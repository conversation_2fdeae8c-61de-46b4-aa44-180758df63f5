import { defineStore } from 'pinia'
import { ref } from 'vue'
import audioService from '@/lib/audioService'

export const useVipPopupStore = defineStore('VipContent', () => {
  const showVipPopup = ref(false)
  
  const openVipPopup = () => {
    audioService.play('button1')
    showVipPopup.value = true
  }
  
  const closeVipPopup = () => {
    audioService.play('button1')
    showVipPopup.value = false
  }

  return {
    showVipPopup,
    openVipPopup,
    closeVipPopup
  }
}) 