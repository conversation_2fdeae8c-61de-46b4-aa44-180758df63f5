<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

interface InventoryItem {
  key: string | null
  icon: string | null
  name?: string
  quantity: number
}

defineProps<{
  item: InventoryItem
  selected?: boolean
}>()

defineEmits<{
  (e: 'click'): void
}>()
</script>

<template>
  <div class="slot" :class="{ selected }" @click="$emit('click')">
    <div v-if="item?.key" class="slot-content">
      <img :src="item.icon" :alt="item.name" />
      <span class="item-quantity">{{ Number(item.quantity).toFixed() }}</span>
    </div>
  </div>
</template>

<style scoped>
.slot {
  width: calc(var(--base-unit) * 60);
  height: calc(var(--base-unit) * 83);
  border-radius: calc(var(--base-unit) * 12);
  background: linear-gradient(180deg, #1F2553 0%, #232A5A 51%, #3A4479 100%);
  border: calc(var(--base-unit) * 3) solid rgba(13, 34, 89, 1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.slot.selected {
  border: calc(var(--base-unit) * 3) solid transparent;
  background: 
    linear-gradient(180deg, #1F2553 0%, #232A5A 51%, #3A4479 100%) padding-box,
    linear-gradient(180deg, #BCC4C6 0%, #E5B568 38%, #D6D99D 100%) border-box;
}

.slot-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.slot-content img {
  width: calc(var(--base-unit) * 48);
  height: calc(var(--base-unit) * 48);
  object-fit: contain;
  margin-bottom: calc(var(--base-unit) * 4);
}

.item-quantity {
  color: white;
  font-size: calc(var(--base-unit) * 14);
  font-weight: bold;
}
</style>
