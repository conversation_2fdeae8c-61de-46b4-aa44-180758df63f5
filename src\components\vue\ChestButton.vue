<template>
    <button :class="['rounded-button', GLOBAL_VARIANT]" @click="selectChestSection">
      <div class="icon"></div>
    </button>
  </template>
  
  <script>
  import { useMenuSectionStore } from '@/stores/gameMenuStore.js';
  import { GLOBAL_VARIANT } from '@/utils/config';
  
  export default {
    setup() {
      const menuSectionStore = useMenuSectionStore();
  
      const selectChestSection = () => {
        menuSectionStore.setSection('Chest'); // Assuming 'Earn' is the chest section

      };
  
      return {
        selectChestSection
      };
    },
    props: {
        variant: { type: String, default: GLOBAL_VARIANT }
    }
  };
  </script>
  

<style scoped>
.rounded-button {
    width: calc(var(--base-unit) * 64);
    height: calc(var(--base-unit) * 48);
    background: rgba(69, 67, 89, 1);
    border: none;
    border-radius: calc(var(--base-unit) * 12) 0px 0px calc(var(--base-unit) * 12);
    box-shadow: 0px calc(var(--base-unit) * 5) calc(var(--base-unit) * 1) rgba(8, 3, 10, 1);
    transition: transform 0.15s ease-out;
}

/* Scale up when pressed */
.rounded-button:active {
    transform: scale(1.1);
}

.icon {
  position: relative;
  top: calc(var(--base-unit) * -33);
  left: 50%;
  transform: translateX(-50%);
  width: calc(var(--base-unit) * 64);
  height: calc(var(--base-unit) * 64);
  background: url('/icon/chest.png') center/cover no-repeat;
  filter: drop-shadow(0 calc(var(--base-unit) * 4) calc(var(--base-unit) * 4) rgba(0, 0, 0, 0.33));
  transition: transform 0.15s ease-out;
}

/* Move icon up when button is pressed */
.rounded-button:active .icon {
    transform: translateX(-50%) translateY(-10%);
}
</style>