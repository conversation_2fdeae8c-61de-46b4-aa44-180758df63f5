<!-- ResourceCounter.vue -->
<template>
    <div class="resource-counter">
        <div class="icon">
            <img :src="icon" alt="resource icon" />
        </div>
        <div class="value-container" :style="gradientStyle">
            <span class="value">{{ value }}</span>
        </div>
    </div>
</template>

<script>
import { defineComponent, computed } from 'vue'

export default defineComponent({
    name: 'ResourceCounter',
    props: {
        icon: {
            type: String,
            required: true
        },
        value: {
            type: String,
            default: '0'
        },
        gradientFromColor: {
            type: String,
            default: 'rgba(43, 18, 13, 1)'
        },
        gradientToColor: {
            type: String,
            default: 'rgba(20, 4, 4, 1)'
        }
    },
    setup(props) {
        const gradientStyle = computed(() => ({
            backgroundImage: `linear-gradient(to right, ${props.gradientFromColor}, ${props.gradientToColor})`
        }))

        return {
            gradientStyle
        }
    }
})
</script>

<style scoped>
.resource-counter {
    position: relative;
    display: flex;
    align-items: center;
    height: calc(var(--base-unit) * 38);
}

.icon {
    position: absolute; 
    left: calc(var(--base-unit) * 0);
    width: calc(var(--base-unit) * 38);
    height: calc(var(--base-unit) * 38);
    flex-shrink: 0;
}

.icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.value-container {
    border-radius: calc(var(--base-unit) * 4);
    width: calc(var(--base-unit) * 64);
    margin-left: calc(var(--base-unit) * 19);
    padding: calc(var(--base-unit) * 2) calc(var(--base-unit) * 8);
    padding-left: calc(var(--base-unit) * 18);

    display: flexbox;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.value {
    color: white;
    font-weight: bold;
    font-size: calc(var(--base-unit) * 12);
    white-space: nowrap;
}
</style>