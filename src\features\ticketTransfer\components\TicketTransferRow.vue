<script setup>
import { AppRow } from '@/components/common';
import { useTicketTransferPopupStore } from '../stores/ticketTransferPopupStore'
import audioService from '@/lib/audioService'
  

const ticketTransferPopupStore = useTicketTransferPopupStore()

const OpenPopup = () => {
  audioService.play('button1'); // Play sound effect
  ticketTransferPopupStore.openTicketTransferPopup()
}
</script>

<template>
    <AppRow :iconSrc="'/icon/ticket.png'" :showArrow="true" :clickable="true" @click="OpenPopup">
      <slot>{{ $t('profile.ticketTransfer') }}</slot>
    </AppRow>
</template>
  
<style scoped>

</style>