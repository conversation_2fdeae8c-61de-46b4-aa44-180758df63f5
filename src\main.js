import { createApp } from 'vue';
import App from './App.vue';
import { createPinia } from 'pinia';
import i18n, { loadLocaleMessages } from '@/lib/i18n'
import router from './router';
import liff from "@line/liff";

window.addEventListener('load', async function () {
    try {
        const isLocal = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

        // Now that the translations are loaded, mount the Vue app
        const app = createApp(App);
        await loadLocaleMessages(i18n.global.locale.value);
        app.use(i18n).use(createPinia()).use(router);
        app.mount('#app');

        liff.init({
            liffId: import.meta.env.VITE_LINE_LIFF_ID,
        });

    } catch (error) {
        console.error("Error initializing application:", error);
    }
});
