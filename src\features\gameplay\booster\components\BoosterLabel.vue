<script setup>

</script>

<template>
  <div class="booster-label">
    <div class="label-text"><slot></slot></div>
  </div>
</template>

<style scoped>
.booster-label {
  background: #A55F3B;
  border: calc(var(--base-unit) * 2) solid #FDD99B;
  border-bottom: none;
  border-top-left-radius: calc(var(--base-unit) * 16);
  border-top-right-radius: calc(var(--base-unit) * 16);
  padding: calc(var(--base-unit) * 4) calc(var(--base-unit) * 18);
  width: fit-content;
}

.label-text {
  color: rgba(255, 255, 255, 1);
  font-size: calc(var(--base-unit) * 20);
  text-shadow: none;
  -webkit-text-stroke: calc(var(--base-unit) * 2) #251B12;
  text-align: center;
}
</style>
