export default (await import('vue')).defineComponent({
    name: 'VersionText',
    data() {
        return {
            version: __APP_VERSION__,
        };
    },
});
const __VLS_ctx = {};
let __VLS_components;
let __VLS_directives;
// CSS variable injection 
// CSS variable injection end 
__VLS_asFunctionalElement(__VLS_intrinsicElements.footer, __VLS_intrinsicElements.footer)({
    ...{ class: "version-container" },
});
__VLS_asFunctionalElement(__VLS_intrinsicElements.span, __VLS_intrinsicElements.span)({
    ...{ class: "version-text" },
});
(__VLS_ctx.version);
/** @type {__VLS_StyleScopedClasses['version-container']} */ ;
/** @type {__VLS_StyleScopedClasses['version-text']} */ ;
var __VLS_dollars;
let __VLS_self;
