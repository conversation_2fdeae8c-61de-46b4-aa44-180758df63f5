
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useJackpotInfoPopupStore = defineStore('jackpotInfoPopup', () => {

  const isJackpotInfoShow= ref(false)
  const openJackpotInfoPopup = () => (isJackpotInfoShow.value = true)
  const closeJackpotInfoPopup = () => (isJackpotInfoShow.value = false)

  return {
    isJackpotInfoShow,
    openJackpotInfoPopup,
    closeJackpotInfoPopup,
  }
})
