import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useNotificationStore } from '@/stores/notificationStore';
import { bindReferralCode } from '../api';
export function useReferralBinding() {
    const { t } = useI18n();
    const notificationStore = useNotificationStore();
    const isBinding = ref(false);
    const bindCode = async (code) => {
        if (!code)
            return false;
        isBinding.value = true;
        try {
            await bindReferralCode(code);
            notificationStore.addNotification({
                type: 'success',
                title: t('referral.applied'),
                message: t('referral.codeAppliedSuccess'),
                duration: 3000
            });
            return true;
        }
        catch (error) {
            console.error('Failed to bind referral code:', error);
            notificationStore.addNotification({
                type: 'error',
                message: t('referral.bindingFailedNoResponse'),
                duration: 3000
            });
            return false;
        }
        finally {
            isBinding.value = false;
        }
    };
    return {
        isBinding,
        bindCode
    };
}
