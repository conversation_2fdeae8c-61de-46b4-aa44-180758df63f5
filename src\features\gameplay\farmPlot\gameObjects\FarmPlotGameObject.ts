// FarmPlot.ts
import { Sprite, Assets, ColorMatrixFilter } from 'pixi.js'; 
import { GameObject, Scene, SpriteRendererComponent, Vector2 } from '@/features/gameplay/shared';
import { AssetService } from '../services/AssetService';
import type { FarmPlotStats } from '../stores/farmPlotStore';
import { Ref, watch } from 'vue';

export class FarmPlotGameObject extends GameObject {
  constructor(scene: Scene, private readonly plot: Ref<FarmPlotStats>) {
    super(scene);
  }

  override async start(): Promise<void> {
    const assets = await AssetService.loadPlotAssets(this.plot.value.plotNumber);
    const texture = await Assets.load(assets.background);

    let spriteRenderer = new SpriteRendererComponent({
      texture,
      anchor: { x: 0.5, y: 0.5 },
    });
    spriteRenderer.scaleRelativeToWidth = false;
    this.addComponent(spriteRenderer);
    spriteRenderer.render(); // initial sync

    // ✅ Watch for unlock state changes and update filter
    watch(
      () => this.plot.value.isUnlocked,
      () => {
        this.updateFilter();
      },
      { immediate: false }
    );

    this.updateFilter();
  }

  private updateFilter() {
    if (!this.transform.gameObject.getComponent(SpriteRendererComponent)) return;

    if (!this.plot.value.isUnlocked) {
      const filter = new ColorMatrixFilter();
      filter.desaturate();
      filter.brightness(0.4, true);
      this.transform.gameObject.getComponent(SpriteRendererComponent).sprite.filters = [filter];
    } else {
      this.transform.gameObject.getComponent(SpriteRendererComponent).sprite.filters = [];
    }
  }

  override onUpdate(): void {}
}
