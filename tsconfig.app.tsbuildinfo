{"root": ["./src/app.vue", "./src/shims-vue.d.ts", "./src/components/topsection.vue", "./src/components/common/appinput.vue", "./src/components/common/appmodal.vue", "./src/components/common/apppagination.vue", "./src/components/common/apppopup.vue", "./src/components/common/approw.vue", "./src/components/common/background.vue", "./src/components/common/closebutton.vue", "./src/components/common/colorbutton.vue", "./src/components/common/datatable.vue", "./src/components/common/decoratedtitlepanel.vue", "./src/components/common/decoratedtitlepanelwithlabelonleft.vue", "./src/components/common/headerlabel.vue", "./src/components/common/highlightbutton.vue", "./src/components/common/labelred.vue", "./src/components/common/orangegradientbutton.vue", "./src/components/common/pluschestamount.vue", "./src/components/common/rankcircle.vue", "./src/components/common/roundedbutton.vue", "./src/components/common/roundedlabel.vue", "./src/components/common/slidemenu.vue", "./src/components/common/tabslayout.vue", "./src/components/common/timerpanel.vue", "./src/components/common/titlepanel.vue", "./src/components/common/titlepanelcolor.vue", "./src/components/jackpot/jackpotpopupoverlay.vue", "./src/components/phaser/cowspine.vue", "./src/components/popup/chestinfocontent.vue", "./src/components/popup/chestoddscontent.vue", "./src/components/utility/versiontext.vue", "./src/components/vue/chestbutton.vue", "./src/components/vue/gamenotification.vue", "./src/components/vue/leaderboardbutton.vue", "./src/components/vue/leaderboardlist.vue", "./src/components/vue/resourcecard.vue", "./src/components/vue/resourcecounter.vue", "./src/components/vue/archived/assetcounterold.vue", "./src/features/auth/components/connectbutton.vue", "./src/features/auth/components/minidappconnectbutton.vue", "./src/features/countdownchest/components/countdownchest.vue", "./src/features/countdownchest/composables/useboostlink.ts", "./src/features/gamemenu/components/gamemenu.vue", "./src/features/gameplay/index.ts", "./src/features/gameplay/animation/tween.ts", "./src/features/gameplay/animation/tweenbuilder.ts", "./src/features/gameplay/animation/tweenmanager.ts", "./src/features/gameplay/animation/index.ts", "./src/features/gameplay/booster/index.ts", "./src/features/gameplay/booster/components/boosterinventorypopup.vue", "./src/features/gameplay/booster/components/boosteritem.vue", "./src/features/gameplay/booster/components/boosterlabel.vue", "./src/features/gameplay/booster/components/boostermodal.vue", "./src/features/gameplay/booster/components/boosterpopup.vue", "./src/features/gameplay/booster/components/boostertimeremaining.vue", "./src/features/gameplay/booster/stores/boosterstore.ts", "./src/features/gameplay/deliveryline/index.ts", "./src/features/gameplay/deliveryline/types.ts", "./src/features/gameplay/deliveryline/components/deliveryline.vue", "./src/features/gameplay/deliveryline/components/deliverylinepanel.vue", "./src/features/gameplay/deliveryline/components/deliverylineupgradepopup.vue", "./src/features/gameplay/deliveryline/gamecomponents/conveyormovercomponent.ts", "./src/features/gameplay/deliveryline/gamecomponents/milkproducecomponent.ts", "./src/features/gameplay/deliveryline/gamecomponents/milkrunnercomponent.ts", "./src/features/gameplay/deliveryline/gameobjects/conveyorgameobject.ts", "./src/features/gameplay/deliveryline/gameobjects/milkgameobject.ts", "./src/features/gameplay/deliveryline/gameobjects/spawnergameobject.ts", "./src/features/gameplay/deliveryline/scenes/usedeliverylinescene.ts", "./src/features/gameplay/deliveryline/stores/deliverylinestore.ts", "./src/features/gameplay/deliveryline/stores/deliverylineupgradepopup.ts", "./src/features/gameplay/farmplot/index.ts", "./src/features/gameplay/farmplot/components/farmgroup.vue", "./src/features/gameplay/farmplot/components/farmplotunlockpopup.vue", "./src/features/gameplay/farmplot/components/farmplotupgradepopup.vue", "./src/features/gameplay/farmplot/components/testbox.vue", "./src/features/gameplay/farmplot/gamecomponents/cowboostcomponent.ts", "./src/features/gameplay/farmplot/gamecomponents/cowproducecomponent.ts", "./src/features/gameplay/farmplot/gamecomponents/cowrunnercomponent.ts", "./src/features/gameplay/farmplot/gamecomponents/farmplotrunnercomponent.ts", "./src/features/gameplay/farmplot/gamecomponents/indexzsettercomponent.ts", "./src/features/gameplay/farmplot/gameobjects/cowgameobject.ts", "./src/features/gameplay/farmplot/gameobjects/farmplotgameobject.ts", "./src/features/gameplay/farmplot/scenes/usefarmgroupscene.ts", "./src/features/gameplay/farmplot/services/assetservice.ts", "./src/features/gameplay/farmplot/stores/farmplotstore.ts", "./src/features/gameplay/farmplot/stores/farmplotunlockpopupstore.ts", "./src/features/gameplay/farmplot/stores/farmplotupgradepopupstore.ts", "./src/features/gameplay/offline/index.ts", "./src/features/gameplay/offline/api/index.ts", "./src/features/gameplay/offline/components/offlinerewardpopup.vue", "./src/features/gameplay/shared/index.ts", "./src/features/gameplay/shared/common/emptygameobject.ts", "./src/features/gameplay/shared/common/poptextgameobject.ts", "./src/features/gameplay/shared/common/spriterenderercomponent.ts", "./src/features/gameplay/shared/common/textobject.ts", "./src/features/gameplay/shared/core/gamecomponent.ts", "./src/features/gameplay/shared/core/gameobject.ts", "./src/features/gameplay/shared/core/transform.ts", "./src/features/gameplay/shared/generic/mathf.ts", "./src/features/gameplay/shared/generic/vector2.ts", "./src/features/gameplay/shared/scene/scene.ts", "./src/features/gameplay/shared/scene/scenemanager.ts", "./src/features/gameplay/shared/template/emptycomponenttemplate.ts", "./src/features/gameplay/shared/template/textconfigtemplate.ts", "./src/features/gameplay/stores/gameplaystatemanager.ts", "./src/features/iap/components/shoplabel.vue", "./src/features/iap/components/shoppaymentappmodal.vue", "./src/features/iap/components/shoppaymentpopup.vue", "./src/features/inventory/api/index.ts", "./src/features/inventory/components/inventorybutton.vue", "./src/features/inventory/components/inventorydescription.vue", "./src/features/inventory/components/inventorygrid.vue", "./src/features/inventory/components/inventoryslot.vue", "./src/features/inventory/components/inventorytabs.vue", "./src/features/inventory/composables/usecrafting.ts", "./src/features/inventory/composables/useinventoryitems.ts", "./src/features/inventory/composables/useinventorytabs.ts", "./src/features/jackpot/components/jackpotinfopopup.vue", "./src/features/jackpot/components/jackpotpanel.vue", "./src/features/kaiawithdraw/components/kaiawithdrawpopup.vue", "./src/features/kaiawithdraw/components/kaiawithdrawrow.vue", "./src/features/leaderboard/components/gemleaderboard.vue", "./src/features/leaderboard/composables/usegemleaderboard.ts", "./src/features/openchest/components/chestanimation.vue", "./src/features/openchest/components/chestitemsview.vue", "./src/features/openchest/components/chestnextview.vue", "./src/features/openchest/components/chestshareview.vue", "./src/features/openchest/components/chestsummaryview.vue", "./src/features/openchest/components/openchestoverlay.vue", "./src/features/referral/components/dailyreferralrewardbutton.vue", "./src/features/referral/components/dailyreferralrewardpanel.vue", "./src/features/referral/components/referralcode.vue", "./src/features/referral/components/referraldetailspopup.vue", "./src/features/referral/components/referralprogressbar.vue", "./src/features/referral/components/referralrewardbutton.vue", "./src/features/referral/components/referralrewardcard.vue", "./src/features/referral/composables/usereferralbinding.ts", "./src/features/referral/composables/usereferraldetails.ts", "./src/features/task/components/taskpanel.vue", "./src/features/tickettransfer/components/tickettransferpopup.vue", "./src/features/tickettransfer/components/tickettransferrow.vue", "./src/features/userinfo/components/gemcounter.vue", "./src/features/userinfo/components/playerlabel.vue", "./src/features/userinfo/components/playerpopup.vue", "./src/features/vip/components/vipappmodal.vue", "./src/features/vip/components/vipcontent.vue", "./src/features/vip/components/viplabel.vue", "./src/features/vip/components/vippopup.vue", "./src/layouts/blanklayout.vue", "./src/layouts/mainlayout.vue", "./src/layouts/menucomponent.vue", "./src/layouts/preloadcomponent.vue", "./src/lib/fetch.ts", "./src/utils/units.ts", "./src/views/shared/authentication.vue", "./src/views/shared/splashscreen.vue", "./src/views/v1/home.vue", "./src/views/v1/inventory.vue", "./src/views/v1/jackpot.vue", "./src/views/v1/leaderboard.vue", "./src/views/v1/referral.vue", "./src/views/v1/task.vue", "./src/views/v1/iap/history.vue", "./src/views/v1/iap/shop.vue", "./src/views/v1/profile/profilesetting.vue", "./src/views/v1/profile/index.vue", "./src/views/v2/chestsection.vue", "./src/views/v2/wheelsection.vue"], "version": "5.8.3"}