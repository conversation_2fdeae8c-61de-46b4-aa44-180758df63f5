import { ref } from 'vue'

export const useInventoryTabs = () => {
  const activeTab = ref(0)
  const selectedItemIndex = ref<number | null>(null)

  const handleTabChange = (index: number) => {
    activeTab.value = index
    selectedItemIndex.value = null
  }

  const selectItem = (index: number) => {
    selectedItemIndex.value = index
  }

  return {
    activeTab,
    selectedItemIndex,
    handleTabChange,
    selectItem
  }
}
