<script setup lang="ts">
import { CloseButton } from '@/components/common'
import BoosterModal from '../components/BoosterModal.vue'

const props = defineProps<{
  isOpen: boolean
}>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

const handleOverlayClick = (e: MouseEvent) => {
  if ((e.target as HTMLElement).classList.contains('popup-overlay')) {
    emit('close')
  }
}
</script>

<template>
  <div class="popup-overlay" v-if="isOpen" @click="handleOverlayClick">
    <div class="popup-container" @click.stop>
      <slot name="modal">
        <BoosterModal class="info-panel">
          <CloseButton class="close-button" @click="emit('close')" />
          <div class="popup-content">
            <slot></slot>
          </div>
        </BoosterModal>
      </slot>
    </div>
  </div>
</template>

<style scoped>
.popup-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.info-panel {
  margin: calc(var(--base-unit) * 20);
}

.popup-container {
  animation: slideUp 0.4s ease-out;
  width: 100%;
  position: relative;
  
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.popup-content {
  text-align: left;
  padding: calc(var(--base-unit) * 8);
}

.close-button {
  position: absolute;
  top: calc(var(--base-unit) * 5);
  right: calc(var(--base-unit) * 5);
  z-index: 10;
}
</style> 