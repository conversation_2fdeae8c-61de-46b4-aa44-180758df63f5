import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import pkg from './package.json';

export default defineConfig({

    base: '/',
    plugins: [
        vue(),
    ],

    define: {
        __APP_VERSION__: JSON.stringify(pkg.version),
    },
    
    server: {
        port: 3000,
        open: true,
    },

    build: {
        outDir: 'dist',
        chunkSizeWarningLimit: 2000,
        emptyOutDir: true,
    },

    // Add optimizeDeps configuration for Phaser
    optimizeDeps: {
        include: ['phaser']
    },

    // Add resolve configuration for asset paths
    resolve: {
        alias: {
            '@': '/src'
        }
    },
    // Configure asset handling
    assetsInclude: ['**/*.png', '**/*.jpg', '**/*.svg'],

    // Configure public directory
    publicDir: 'public',
});