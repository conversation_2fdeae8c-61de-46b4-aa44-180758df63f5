<script setup>
import VersionText from '@/components/utility/VersionText.vue';
// Remove Background import since we're using video directly
// import { Background } from '@/components/common';

import { onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import audioService from '@/lib/audioService'
import MiniDappConnectButton from '@/features/auth/components/MiniDappConnectButton.vue'
import { useWalletAuth } from '@/features/auth/composables/useWalletAuth'
import { useUser } from '@/features/auth/composables/useUser'

const { isConnected, processShareCodes } = useWalletAuth()
const { user, loadUser } = useUser();
const router = useRouter()

let userInteracted = false

function handleUserInteraction() {
  if (!userInteracted) {
    userInteracted = true
    setTimeout(() => {
      audioService.playBGM()
    }, 500)
  }
}

const handleSuccessfulConnect = async () => {
  await loadUser();
  await processShareCodes();
  if (user.value) {
    router.push('/v1');
  }
};

watch(isConnected, (newIsConnected) => {
  if (newIsConnected) {
    handleSuccessfulConnect();
  }
});

onMounted(() => {
  document.addEventListener('click', handleUserInteraction)
  document.addEventListener('touchstart', handleUserInteraction)
})
</script>

<template>
  <div class="container">
    <img class="intro-image" src="/img/intro.png" alt="Intro Background" />

    <video
      autoplay
      muted
      loop
      playsinline
      webkit-playsinline
      class="intro-image"
    >
      <source src="/video/login_animation.mp4" type="video/mp4" />
    </video>
    <img class="logo" src="/img/logo.png" alt="Logo" />
    <VersionText />
    <div class="button-container">
        <MiniDappConnectButton />
    </div>
  </div>
</template>

<style scoped>
.container {
  position: relative;
  width: 100%;
  height: var(--app-height);
  overflow: hidden;
}

.intro-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -2;
}
.button-container {
  position: absolute;
  bottom: calc(var(--base-unit) * 48);
  width: 100%;
  display: flex;
  justify-content: center;
}

.logo {
  position: absolute;
  top: calc(var(--base-unit) * 16);
  left: calc(50% - calc(var(--base-unit) * 128));
  width: calc(var(--base-unit) * 256);
  height: calc(var(--base-unit) * 256);
  object-fit: contain;
  z-index: 1;
  animation: pulse 1s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.05);
  }
}
</style>