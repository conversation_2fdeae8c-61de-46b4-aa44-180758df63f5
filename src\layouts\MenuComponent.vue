<template>
  <Background :imagePath="'/img/menu-background.svg'" :style="'backgroundColor:rgba(44, 55, 87, 1)'"/>
  <GameNotification />
  <TopSection/>
  <div class="section-container">
    <component 
      :is="activeSection.component" 
      v-bind="activeSection.props" 
    />
  </div>
  <GameMenu ref="gameMenu"/>
  <OpenChestOverlay ref="chestOverlay"/>
  <FourChestPopupOverlay/>
  <PopupManager />
</template>

<script>
import { useMenuSectionStore } from '@/stores/gameMenuStore.js';
import { useNotificationStore } from '@/stores/notificationStore.js';
import { ref } from 'vue';
import { Background } from '@/components/common';
import GameMenu from '../components/vue/GameMenu.vue';
import ChestSection from './pages/ChestSection.vue';
import EarnSection from './pages/EarnSection.vue';
import LeaderboardSection from './pages/LeaderboardSection.vue';
import ComingSoonSection from './pages/ComingSoonSection.vue';
import WheelSection from './pages/WheelSection.vue';
import InviteSection from './pages/InviteSection.vue';
import ProfileSection from './pages/ProfileSection.vue';
import SettingSection from './pages/SettingSection.vue';
import TopSection from './TopSection.vue';
import HomeSection from './pages/HomeSection.vue';
import GameNotification from './vue/GameNotification.vue';
import OpenChestOverlay from './ui/OpenChestOverlay.vue';
import InventorySection from './pages/InventorySection.vue';
import JackpotChestSection from './pages/JackpotChestSection.vue';
import FourChestPopupOverlay from './jackpot/FourChestPopupOverlay.vue';
import PopupManager from './popup/PopupManager.vue';


export default {
  name: "MenuComponent",
  setup() {
    const menuSectionStore = useMenuSectionStore();
    const notificationStore = useNotificationStore();

    return { menuSectionStore, notificationStore };
  },
  computed: {
    currentSection() {
      return this.menuSectionStore.currentSection;
    },
    activeSection() {
      const sections = {
        Home: { component: HomeSection, props: {} },
        Earn: { component: EarnSection, props: {} },
        Chest: { component: ChestSection, props: {} },
        Wheel: { component: JackpotChestSection, props: { } },
        Invite: { component: InviteSection },
        Profile: { component: ProfileSection },
        Setting: { component: SettingSection },
        Inventory: {component: InventorySection },
        HolderLeaderboard: { component: ComingSoonSection, props: { title: 'Holder Leaderboard' } },
        JackpotPool: { component: ComingSoonSection, props: { title: 'Jackpot Pool' } },
        IndividualInfluencerPool: { component: ComingSoonSection, props: { title: 'Individual Influencer Pool' } },
        TeamInfluencerPool: { component: ComingSoonSection, props: { title: 'Team Influencer Pool' } }
      };

      return sections[this.currentSection] || { component: HomeSection, props: {} };
    }
  },
  components: {
    Background,
    GameMenu,
    ChestSection,
    EarnSection,
    LeaderboardSection,
    ComingSoonSection,
    InviteSection,
    ProfileSection,
    SettingSection,
    JackpotChestSection,
    TopSection,
    HomeSection,
    GameNotification,
    WheelSection,
    OpenChestOverlay,
    InventorySection,
    FourChestPopupOverlay,
    PopupManager
  }
};
</script>

<style scoped>
.section-container {
  position: fixed;
  width: var(--app-width);
  display: flex;
  flex-direction: column;
  height: calc(var(--app-height) - calc(var(--base-unit) * (124 + 76))); /* Adjust height dynamically */
  
  overflow-y: auto; /* Make only the content inside scroll */
  overflow-x: hidden;
  top: calc(var(--base-unit) * 124);
  bottom: calc(var(--base-unit) * 76);
}
</style>