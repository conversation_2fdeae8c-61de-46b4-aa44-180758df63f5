<template>
  <button 
    class="button-container"
    @click="$emit('click')"
    :disabled="disabled"
  >
    <div class="button-inner main-text" :style="{ padding }">
      <slot></slot>
    </div>
  </button>
</template>

<script setup lang="ts">
defineProps<{
  disabled?: boolean
  padding?: string
}>()

    const emit = defineEmits<{
    (e: 'click'): void
    }>()
</script>


<style scoped>
.button-container {
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  padding: calc(var(--base-unit) * 4);
  background: linear-gradient(180deg, #FEE87B 0%, #FD9C51 100%);

  border: calc(var(--base-unit) * 2) solid #773D2A;
  border-radius: calc(var(--base-unit) * 8);
  transition: transform 0.1s ease, box-shadow 0.1s ease;
}

.button-inner {
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-size: calc(var(--base-unit) * 14);
  padding: calc(var(--base-unit) * 4) calc(var(--base-unit) * 12);
  background: linear-gradient(180deg, #FEB644 0%, #FB873A 100%);
  border-radius: calc(var(--base-unit) * 8);
  box-sizing: border-box;
}

/* Press effect */
.button-container:active {
transform: scale(0.95); /* Slightly shrink button */
box-shadow: inset 0 0 calc(var(--base-unit) * 82) rgba(0, 0, 0, 0.2); /* Add inset shadow */
}
</style>