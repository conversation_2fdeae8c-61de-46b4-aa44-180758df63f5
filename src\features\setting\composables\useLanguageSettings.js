import { useI18n } from 'vue-i18n'
import { computed } from 'vue'
import { loadLocaleMessages } from '@/lib/i18n'
import audioService from '@/lib/audioService'

export const useLanguageSettings = () => {
  const { locale, t } = useI18n()

  const toggleLanguage = async () => {
    audioService.play('button1')
    let newLocale
    switch (locale.value) {
      case 'en':
        newLocale = 'ja'
        break
      case 'ja':
        newLocale = 'zh'
        break
      case 'zh':
        newLocale = 'en'
        break
      default:
        newLocale = 'en'
    }
    await loadLocaleMessages(newLocale)
  }

  const currentLanguageDisplay = computed(() => {
    switch (locale.value) {
      case 'en':
        return 'English'
      case 'ja':
        return '日本語'
      case 'zh':
        return '中文'
      default:
        return 'English'
    }
  })

  return {
    toggleLanguage,
    currentLanguageDisplay,
    t,
  }
}
