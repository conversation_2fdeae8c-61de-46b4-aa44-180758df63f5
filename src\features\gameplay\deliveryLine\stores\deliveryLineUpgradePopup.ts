import type { DeliveryLineStats } from '@/features/gameplay/api';
import type { FarmPlotStats } from '@/features/gameplay/api';
import { defineStore } from 'pinia';
export interface DeliveryLinePopupState {
  isDeliveryLineUpgradeShow: boolean;
}

export const useDeliveryLineUpgradePopupStore = defineStore('deliveryLineUpgradePopup', {
  state: (): DeliveryLinePopupState => ({
    isDeliveryLineUpgradeShow: false,
  }),

  actions: {
    openDeliveryLineUpgradePopup() {
      this.isDeliveryLineUpgradeShow = true;
    },

    closeDeliveryLineUpgradePopup() {
      this.isDeliveryLineUpgradeShow = false;
    },
  },
});
