<template>
  <div ref="container" class="test-box"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { Sprite, Texture, Assets } from 'pixi.js';
import { Scene, GameObject } from '@/features/gameplay/shared';
import { EmptyGameObject, Vector2 } from '@/features/gameplay/shared';
import { SpriteRendererComponent } from '@/features/gameplay/shared/common/SpriteRendererComponent';
import { TextObject, PopTextGameObject } from '@/features/gameplay/shared';


const container = ref<HTMLElement | null>(null);
let scene: Scene | null = null;


onMounted(async () => {
  if (!container.value) return;

  // Initialize scene
  scene = new Scene(container.value, {
    width: container.value.offsetWidth,
    height: container.value.offsetHeight,
    backgroundColor: 0x000000,
    antialias: true,
  });

    // Start the scene
    await scene.init();
  
  // Create empty game object
  const gameObject = new EmptyGameObject(scene);
  GameObject.instantiate(gameObject);

  TextObject.instantiate(new TextObject(scene, {
    text: 'Hello',
    position: new Vector2(200, 0.5),
    style: {
      fontSize: 24,
    },
  }));
  

  // Add sprite renderer component
  const texture = await Assets.load('/assets/farmPlot/farm-plot-1.png');
  const spriteRenderer = new SpriteRendererComponent({
    texture: texture,
    anchor: { x: 0.5, y: 0.5 },
  });
  gameObject.addComponent(spriteRenderer);
  gameObject.transform.scale = new Vector2(0.5, 0.5);
  gameObject.transform.position = new Vector2(0.5, 0.5);

  // Create empty game object

  const gameObject2 = new EmptyGameObject(scene);
  GameObject.instantiate(gameObject2);
  
  // Add sprite renderer component
  const texture2 = await Assets.load('/assets/farmPlot/farm-plot-2.png');
  const spriteRenderer2 = new SpriteRendererComponent({
    texture: texture2,
    anchor: { x: 0.5, y: 0.5 },
  });
  gameObject2.addComponent(spriteRenderer2);
  gameObject2.transform.parent = gameObject.transform;
  gameObject2.transform.scale = new Vector2(0.5, 0.5);
  gameObject2.transform.position = new Vector2(0.5, 0.5);

  // Create empty game object

  const gameObject3 = new EmptyGameObject(scene);
  GameObject.instantiate(gameObject3);
  
  // Add sprite renderer component
  const texture3 = await Assets.load('/assets/farmPlot/farm-plot-3.png');
  const spriteRenderer3 = new SpriteRendererComponent({
    texture: texture3,
    anchor: { x: 0.5, y: 0.5 },
  });
  gameObject3.addComponent(spriteRenderer3);
  gameObject3.transform.parent = gameObject2.transform;
  gameObject3.transform.scale = new Vector2(0.5, 0.5);
  gameObject3.transform.position = new Vector2(0.5, 0.5);

  const gameObject4 = new PopTextGameObject(scene, {
    text: 'Hello',
    position: new Vector2(0.5, 0.5),
    style: {
      fill: '#ffffff',
      fontSize: 24,
    },
  });
  GameObject.instantiate(gameObject4);
  gameObject4.transform.parent = gameObject3.transform;
  gameObject4.transform.scale = new Vector2(1, 1);
  gameObject4.transform.position = new Vector2(0.5,0.5);

});


onBeforeUnmount(() => {
  // Clean up scene when component is unmounted
  if (scene) {
    scene.destroy();
    scene = null;
  }
});
</script>

<style scoped>
.test-box {
  width: calc(var(--base-unit) * 400);
  height: calc(var(--base-unit) * 300);
  position: relative;
}
</style>
