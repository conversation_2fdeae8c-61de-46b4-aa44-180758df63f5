// useWalletAuth.js
import { ref } from 'vue'
import { getAuthMessage, verifySignature } from '../api'
import { useToken } from './useToken'
import { useUser } from './useUser'
import { useDappPortalSdk } from './useDappPortalSdk'
import { useReferralBinding } from '@/features/referral'
import { useBoostLink } from '@/features/countdownChest'
import { useShopStore } from '@/features/iap'

const isConnected = ref(false)
const isProcessingCodes = ref(false)

export const useWalletAuth = () => {
  const { token, isTokenExpired, loadToken, setToken, clearToken } = useToken()
  const { user, loadUser, isWalletMatch, setUser, remove } = useUser()
  const { init, getWalletAddress, requestWalletAccess, signMessage, disconnectWallet } = useDappPortalSdk()
  const { bindCode: bindReferralCode } = useReferralBinding()
  const { processBoostCode } = useBoostLink()
  const shopStore = useShopStore()

  const loadConnection = async () => {
    await init()
    loadToken()
    loadUser()

    if (!token.value || isTokenExpired(token.value) || !user.value) {
      isConnected.value = false
      return false
    }

    try {
      const wallet = await getWalletAddress()
      const match = isWalletMatch(wallet)
      isConnected.value = !!(wallet && match)
      
      if (isConnected.value) {
        try {
          await shopStore.initializeShop()
        } catch (error) {
          console.error('Failed to load shop data on reconnection:', error)
        }
      }

      return isConnected.value
    } catch {
      isConnected.value = false
      return false
    }
  }

  const processShareCodes = async () => {
    if (isProcessingCodes.value) return
    isProcessingCodes.value = true

    try {
      // Get URL parameters
      const urlParams = new URLSearchParams(window.location.search)
      const refParam = urlParams.get('ref')

      if (refParam) {
        console.log('refParam', refParam)
        // Check if it's a referral code (starts with r_)
        if (refParam.startsWith('r_')) {
          const referralCode = refParam.substring(2) // Remove 'r_' prefix
          try {
            await bindReferralCode(referralCode)
            console.log('Referral code processed:', referralCode)
          } catch (error) {
            console.error('Failed to process referral code:', error)
          }
        }
        // Check if it's a boost code (starts with b_)
        else if (refParam.startsWith('b_')) {
          const boostCode = refParam.substring(2) // Remove 'b_' prefix
          try {
            await processBoostCode(boostCode)
            console.log('Boost code processed:', boostCode)
          } catch (error) {
            console.error('Failed to process boost code:', error)
          }
        }
        // Handle legacy referral codes without prefix
        else {
          try {
            await bindReferralCode(refParam)
            console.log('Legacy referral code processed:', refParam)
          } catch (error) {
            console.error('Failed to process legacy referral code:', error)
          }
        }

        // Remove the ref parameter after processing
        urlParams.delete('ref')
      }

      // Update URL without the processed parameters
      const newUrl = window.location.pathname + (urlParams.toString() ? `?${urlParams.toString()}` : '')
      window.history.replaceState({}, '', newUrl)
    } finally {
      isProcessingCodes.value = false
    }
  }

  const connect = async () => {
    
    let wallet = await getWalletAddress()
    console.log('wallet', wallet)
    if (!wallet) {
      wallet = await requestWalletAccess()
    }
    wallet = await requestWalletAccess()
    const { message } = await getAuthMessage(wallet)
    const signature = await signMessage(message, wallet)
    const { token: newToken, user: newUser } = await verifySignature(wallet, message, signature)

    setToken(newToken)
    setUser(newUser)
    isConnected.value = true

    console.log("authenticated: ", { address: wallet, token: newToken, user: newUser })

    // Process share codes after successful connection
    await processShareCodes()

    // 🎯 自动初始化商店数据
    try {
      await shopStore.initializeShop()
      console.log('Shop data loaded after wallet connection')
    } catch (error) {
      console.error('Failed to load shop data after wallet connection:', error)
    }

    return { address: wallet, token: newToken, user: newUser }
  }

  const disconnect = async () => {
    clearToken()
    remove()
    isConnected.value = false
    await disconnectWallet()
    
    try {
      shopStore.resetStore()
    } catch (error) {
      console.error('Failed to clear shop data after disconnection:', error)
    }
  }

  return {
    loadConnection,
    connect,
    disconnect,
    isConnected,
    processShareCodes
  }
}
