# Dependencies
node_modules
.pnp
.pnp.js

# Environment
.env
.env.local
.env.*.local

# Build outputs
dist
dist-ssr
*.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Testing
coverage
/cypress/videos/
/cypress/screenshots/

# Temporary files
*.tmp
*.temp
.cache