# MooFun 项目 API 接口清单

## 项目概述
- **项目名称**: MooFun Project
- **技术栈**: Vue 3 + Vite + Axios
- **API基础URL**: `import.meta.env.VITE_API_BASE_URL`
- **认证方式**: Bearer <PERSON>ken
- **语言支持**: Accept-Language 头部支持多语言

## HTTP客户端配置

### 基础配置
- **文件位置**: `src/lib/fetch.ts`
- **基础URL**: 通过环境变量 `VITE_API_BASE_URL` 配置
- **请求头**: 
  - `Content-Type: application/json`
  - `Authorization: Bearer {token}` (如果存在)
  - `Accept-Language: {lang}` (默认为 'en')

### 拦截器
- **响应拦截器**: 自动处理401错误，清除token和用户信息
- **请求拦截器**: 自动添加认证token和语言设置

## API接口分类

### 1. 用户认证模块 (Authentication)

#### Web3认证
- **获取认证消息**
  - 端点: `POST /web3-auth/nonce`
  - 参数: `{ walletAddress: string }`
  - 用途: 获取用于钱包签名的nonce和消息
  - 文件: `src/features/auth/api/index.js:3`

- **验证签名登录**
  - 端点: `POST /web3-auth/login`
  - 参数: `{ walletAddress: string, message: string, signature: string }`
  - 用途: 验证钱包签名并获取JWT token
  - 文件: `src/features/auth/api/index.js:7`

- **更新用户名**
  - 端点: `POST /web3-auth/update-username`
  - 参数: `{ newUsername: string }`
  - 用途: 更新用户显示名称
  - 文件: `src/features/userInfo/api/index.js:7`

### 2. 用户信息模块 (User Info)

- **获取用户信息**
  - 端点: `GET /user/me`
  - 用途: 获取当前用户的详细信息
  - 文件: `src/features/userInfo/api/index.js:3`

### 3. 游戏玩法模块 (Gameplay)

#### 配送线管理
- **获取配送线信息**
  - 端点: `GET /delivery/delivery-line`
  - 用途: 获取配送线当前状态
  - 文件: `src/features/gameplay/api/index.js:3`

- **升级配送线**
  - 端点: `POST /delivery/delivery-line/upgrade`
  - 用途: 升级配送线等级
  - 文件: `src/features/gameplay/api/index.js:7`

#### 农场地块管理
- **获取农场地块**
  - 端点: `GET /farm/farm-plots`
  - 用途: 获取所有农场地块信息
  - 文件: `src/features/gameplay/api/index.js:12`

- **升级农场地块**
  - 端点: `POST /farm/farm-plots/upgrade`
  - 参数: `{ plotNumber: number }`
  - 用途: 升级指定地块
  - 文件: `src/features/gameplay/api/index.js:16`

- **解锁农场地块**
  - 端点: `POST /farm/farm-plots/unlock`
  - 参数: `{ plotNumber: number }`
  - 用途: 解锁新的农场地块
  - 文件: `src/features/gameplay/api/index.js:20`

#### 钱包资源管理
- **增加牛奶**
  - 端点: `POST /wallet/increase-milk`
  - 参数: `{ pendingMilk: number }`
  - 用途: 增加用户牛奶资源
  - 文件: `src/features/gameplay/api/index.js:25`

- **增加宝石**
  - 端点: `POST /wallet/increase-gem`
  - 参数: `{ milkAmount: number }`
  - 用途: 将牛奶转换为宝石
  - 文件: `src/features/gameplay/api/index.js:29`

- **批量更新资源**
  - 端点: `POST /wallet/batch-update-resources`
  - 参数: `{ gemRequest: object, milkOperations: array }`
  - 用途: 批量更新多种资源
  - 文件: `src/features/gameplay/api/index.js:33`

#### 离线奖励
- **获取离线奖励**
  - 端点: `GET /wallet/offline-reward`
  - 用途: 获取用户离线期间的奖励信息
  - 文件: `src/features/gameplay/offline/api/index.ts:3`

- **领取离线奖励**
  - 端点: `POST /wallet/claim-offline-reward`
  - 用途: 领取离线奖励
  - 文件: `src/features/gameplay/offline/api/index.ts:7`

### 4. 应用内购买模块 (IAP)

#### 商店产品
- **获取商店产品**
  - 端点: `GET /iap/store/products`
  - 用途: 获取所有可购买的产品列表
  - 文件: `src/features/iap/api/index.js:4`

- **创建支付**
  - 端点: `POST /iap/payment/create`
  - 参数: `{ productId: string, imageUrl: string, paymentMethod: string, testMode: boolean }`
  - 用途: 创建支付订单
  - 文件: `src/features/iap/api/index.js:10`

#### 增强道具管理
- **获取用户增强道具**
  - 端点: `GET /iap/boosters`
  - 用途: 获取用户拥有的增强道具
  - 文件: `src/features/iap/api/index.js:27`

- **获取激活的增强道具**
  - 端点: `GET /iap/boosters/active`
  - 用途: 获取当前激活的增强道具
  - 文件: `src/features/iap/api/index.js:32`

- **使用增强道具**
  - 端点: `POST /iap/boosters/use`
  - 参数: `{ boosterId: string }`
  - 用途: 使用指定的增强道具
  - 文件: `src/features/iap/api/index.js:37`

#### VIP会员
- **获取VIP状态**
  - 端点: `GET /iap/vip/status`
  - 用途: 获取用户VIP会员状态
  - 文件: `src/features/iap/api/index.js:42`

#### 购买历史
- **获取购买历史**
  - 端点: `GET /iap/purchase/history`
  - 参数: `{ page: number, limit: number }` (查询参数)
  - 用途: 获取用户购买历史记录
  - 文件: `src/features/iap/api/index.js:47`

### 5. 倒计时宝箱模块 (Countdown Chest)

- **获取倒计时宝箱**
  - 端点: `GET /jackpot-chest/countdown`
  - 用途: 获取倒计时宝箱状态
  - 文件: `src/features/countdownChest/api/index.js:3`

- **收集倒计时宝箱**
  - 端点: `POST /jackpot-chest/collect`
  - 用途: 收集倒计时宝箱奖励
  - 文件: `src/features/countdownChest/api/index.js:7`

- **加速倒计时宝箱**
  - 端点: `POST /jackpot-chest/accelerate`
  - 参数: `{ seconds: number }`
  - 用途: 使用道具加速倒计时
  - 文件: `src/features/countdownChest/api/index.js:11`

- **触发提升链接**
  - 端点: `POST /telegram-share/boost`
  - 参数: `{ code: string }`
  - 用途: 处理Telegram分享提升代码
  - 文件: `src/features/countdownChest/api/index.js:15`

### 6. 任务系统模块 (Tasks)

- **获取任务列表**
  - 端点: `GET /tasks`
  - 用途: 获取用户可完成的任务列表
  - 文件: `src/features/task/api/index.js:3`

- **完成任务**
  - 端点: `POST /tasks/complete`
  - 参数: `{ taskId: string }`
  - 用途: 标记任务为完成状态
  - 文件: `src/features/task/api/index.js:7`

### 7. 推荐系统模块 (Referral)

#### 推荐状态
- **获取推荐状态**
  - 端点: `GET /referral/status`
  - 用途: 获取用户推荐系统状态
  - 文件: `src/features/referral/api/index.js:3`

- **绑定推荐码**
  - 端点: `POST /referral/bind`
  - 参数: `{ code: string }`
  - 用途: 绑定推荐人代码
  - 文件: `src/features/referral/api/index.js:19`

#### 推荐宝箱
- **获取推荐宝箱数量**
  - 端点: `GET /referral/referral-chests/count`
  - 用途: 获取可用推荐宝箱数量
  - 文件: `src/features/referral/api/index.js:7`

- **领取每日推荐宝箱**
  - 端点: `POST /invite/claim-daily`
  - 用途: 领取每日推荐奖励
  - 文件: `src/features/referral/api/index.js:11`

- **打开推荐宝箱**
  - 端点: `POST /referral/open`
  - 用途: 打开推荐宝箱获取奖励
  - 文件: `src/features/referral/api/index.js:15`

#### 推荐列表
- **获取推荐列表**
  - 端点: `GET /referral/list`
  - 用途: 获取用户的推荐记录
  - 文件: `src/features/referral/api/index.js:23`

- **获取下线列表**
  - 端点: `GET /referral/downline`
  - 参数: `{ level: number, page: number, pageSize: number }` (查询参数)
  - 用途: 获取指定层级的下线用户列表
  - 文件: `src/features/referral/api/index.js:27`

### 8. 门票转移模块 (Ticket Transfer)

- **获取免费门票剩余限制**
  - 端点: `GET /free-ticket/remaining-limit`
  - 用途: 获取免费门票转移的剩余次数
  - 文件: `src/features/ticketTransfer/api/index.js:3`

- **转移门票**
  - 端点: `POST /free-ticket/transfer`
  - 参数: `{ toWalletAddress: string, amount: number }`
  - 用途: 向指定钱包地址转移门票
  - 文件: `src/features/ticketTransfer/api/index.js:7`

### 9. 库存管理模块 (Inventory)

- **使用碎片制作门票**
  - 端点: `POST /fragment/craft-ticket`
  - 参数: `{ fragmentType: string, quantity: number }`
  - 用途: 使用碎片制作游戏门票
  - 文件: `src/features/inventory/api/index.ts:3`

### 10. 排行榜模块 (Leaderboard)

- **获取宝石排行榜**
  - 端点: `GET /gem-leaderboard`
  - 参数: `{ limit: number, offset: number }` (查询参数)
  - 用途: 获取宝石数量排行榜
  - 文件: `src/features/leaderboard/api/index.js:3`

## 第三方服务集成

### 1. KAIA区块链集成
- **SDK**: `@linenext/dapp-portal-sdk`
- **配置文件**: `src/features/auth/composables/useDappPortalSdk.js`
- **功能**:
  - 钱包连接: `kaia_requestAccounts`
  - 钱包地址获取: `kaia_accounts`
  - 消息签名: `personal_sign`
  - 支付处理: `startPayment`
  - 钱包断开: `disconnectWallet`

### 2. LINE LIFF集成
- **SDK**: `@line/liff`
- **配置**: `VITE_LINE_LIFF_ID`
- **初始化**: `src/main.js:18`

### 3. 社交媒体链接
- **Telegram Bot**: `https://t.me/{VITE_TELEGRAM_BOT_USERNAME}`
- **Telegram频道**: `VITE_TELEGRAM_CHANNEL`
- **Twitter**: `VITE_TWITTER_HANDLE`

## 环境变量配置

### 必需的环境变量
```bash
# API配置
VITE_API_BASE_URL=https://api.moofun.app

# 应用域名
VITE_APP_DOMAIN=https://moofun.app

# DApp客户端ID
VITE_DAPP_CLIENT_ID=your_client_id

# Telegram配置
VITE_TELEGRAM_BOT_USERNAME=moofun_bot
VITE_TELEGRAM_CHANNEL=https://t.me/MooFun

# LINE LIFF配置
VITE_LINE_LIFF_ID=moofun

# 社交媒体
VITE_TWITTER_HANDLE=https://x.com/MooFunGame

# 平台检测
VITE_KAIA_ENVIRONMENT=true
```

## 国际化支持

### 语言文件加载
- **端点**: `GET /locales/{locale}.json`
- **HTTP方法**: GET
- **参数**: 无（通过URL路径传递语言代码）
- **响应格式**: JSON
- **用途**: 动态加载指定语言的翻译文件
- **实现**: `src/lib/i18n.js:38`
- **特殊说明**: 使用原生fetch而非axios实例，因为这是静态资源请求
- **支持语言**: 通过Accept-Language头部传递到其他API

## 错误处理

### 通用错误处理
- **401错误**: 自动清除token和用户信息，重定向到认证页面
- **网络错误**: 通过通知系统显示错误消息
- **支付错误**: 特殊处理支付相关错误码

### 支付错误码
- `-32000`: 余额不足
- `-31001`: 用户取消
- `-31002`: 支付失败

## API调用模式

### 标准模式
```javascript
// 基本GET请求
export const getData = () => {
  return fetch.get('/endpoint').then(res => res.data)
}

// 带参数POST请求
export const postData = (params) => {
  return fetch.post('/endpoint', params).then(res => res.data)
}
```

### 异步处理模式
```javascript
// 异步错误处理
export const asyncApiCall = async (params) => {
  try {
    const response = await fetch.post('/endpoint', params)
    return response.data || response
  } catch (error) {
    console.error('API error:', error)
    throw error
  }
}
```

## API调用统计

### 按模块统计
- **用户认证**: 3个接口
- **用户信息**: 1个接口
- **游戏玩法**: 8个接口
- **应用内购买**: 7个接口
- **倒计时宝箱**: 4个接口
- **任务系统**: 2个接口
- **推荐系统**: 6个接口
- **门票转移**: 2个接口
- **库存管理**: 1个接口
- **排行榜**: 1个接口
- **国际化**: 1个接口

**总计**: 36个REST API接口

### 按HTTP方法统计
- **GET请求**: 16个
- **POST请求**: 20个

### 按实现方式统计
- **Axios实例**: 35个接口
- **原生Fetch**: 1个接口（语言文件加载）

## 安全考虑

### 认证机制
- 使用JWT Bearer Token进行API认证
- Token存储在localStorage中
- 自动在请求头中添加Authorization
- 401错误时自动清除认证信息

### 数据验证
- 所有API请求使用JSON格式
- 钱包地址验证通过区块链签名
- 支付操作需要额外的安全验证

## 性能优化

### 请求优化
- 使用axios实例复用连接
- 响应拦截器自动提取data字段
- 批量资源更新接口减少请求次数

### 缓存策略
- 用户信息缓存在localStorage
- 商店数据在钱包连接后自动加载
- 语言文件按需加载并缓存

## 开发建议

### API调用最佳实践
1. 使用统一的fetch实例进行API调用
2. 在组件中使用try-catch处理API错误
3. 重要操作前检查用户认证状态
4. 支付相关操作需要额外的错误处理

### 测试建议
1. 为每个API接口编写单元测试
2. 测试认证失败的错误处理
3. 测试网络异常情况
4. 验证支付流程的完整性

## API端点快速索引

### 按字母顺序排列的所有端点

| 端点 | 方法 | 模块 | 用途 |
|------|------|------|------|
| `/delivery/delivery-line` | GET | 游戏玩法 | 获取配送线信息 |
| `/delivery/delivery-line/upgrade` | POST | 游戏玩法 | 升级配送线 |
| `/farm/farm-plots` | GET | 游戏玩法 | 获取农场地块 |
| `/farm/farm-plots/unlock` | POST | 游戏玩法 | 解锁农场地块 |
| `/farm/farm-plots/upgrade` | POST | 游戏玩法 | 升级农场地块 |
| `/fragment/craft-ticket` | POST | 库存管理 | 制作门票 |
| `/free-ticket/remaining-limit` | GET | 门票转移 | 获取转移限制 |
| `/free-ticket/transfer` | POST | 门票转移 | 转移门票 |
| `/gem-leaderboard` | GET | 排行榜 | 宝石排行榜 |
| `/iap/boosters` | GET | 应用内购买 | 获取增强道具 |
| `/iap/boosters/active` | GET | 应用内购买 | 获取激活道具 |
| `/iap/boosters/use` | POST | 应用内购买 | 使用增强道具 |
| `/iap/payment/create` | POST | 应用内购买 | 创建支付 |
| `/iap/purchase/history` | GET | 应用内购买 | 购买历史 |
| `/iap/store/products` | GET | 应用内购买 | 商店产品 |
| `/iap/vip/status` | GET | 应用内购买 | VIP状态 |
| `/invite/claim-daily` | POST | 推荐系统 | 领取每日奖励 |
| `/jackpot-chest/accelerate` | POST | 倒计时宝箱 | 加速倒计时 |
| `/jackpot-chest/collect` | POST | 倒计时宝箱 | 收集宝箱 |
| `/jackpot-chest/countdown` | GET | 倒计时宝箱 | 获取宝箱状态 |
| `/locales/{locale}.json` | GET | 国际化 | 加载语言文件 |
| `/referral/bind` | POST | 推荐系统 | 绑定推荐码 |
| `/referral/downline` | GET | 推荐系统 | 获取下线列表 |
| `/referral/list` | GET | 推荐系统 | 获取推荐列表 |
| `/referral/open` | POST | 推荐系统 | 打开推荐宝箱 |
| `/referral/referral-chests/count` | GET | 推荐系统 | 推荐宝箱数量 |
| `/referral/status` | GET | 推荐系统 | 推荐状态 |
| `/tasks` | GET | 任务系统 | 获取任务列表 |
| `/tasks/complete` | POST | 任务系统 | 完成任务 |
| `/telegram-share/boost` | POST | 倒计时宝箱 | 处理提升链接 |
| `/user/me` | GET | 用户信息 | 获取用户信息 |
| `/wallet/batch-update-resources` | POST | 游戏玩法 | 批量更新资源 |
| `/wallet/claim-offline-reward` | POST | 游戏玩法 | 领取离线奖励 |
| `/wallet/increase-gem` | POST | 游戏玩法 | 增加宝石 |
| `/wallet/increase-milk` | POST | 游戏玩法 | 增加牛奶 |
| `/wallet/offline-reward` | GET | 游戏玩法 | 获取离线奖励 |
| `/web3-auth/login` | POST | 用户认证 | 验证签名登录 |
| `/web3-auth/nonce` | POST | 用户认证 | 获取认证消息 |
| `/web3-auth/update-username` | POST | 用户认证 | 更新用户名 |

## 扩展计划

### 可能的新增接口
- WebSocket实时通信
- 推送通知API
- 数据分析API
- 社交功能API

### API版本管理
- 当前使用的是v1版本的API
- 建议在URL中包含版本号
- 向后兼容性考虑

---

**文档生成时间**: 2025-07-10
**项目版本**: 1.1.8
**最后更新**: 基于当前代码库分析生成
**API总数**: 36个REST接口 + 区块链SDK集成 + 第三方服务集成
