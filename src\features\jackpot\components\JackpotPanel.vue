<script lang="ts" setup>
import { OrangeGradientButton } from '@/components/common'
import { useRouter } from 'vue-router'
import audioService from '@/lib/audioService'
import { useJackpotInfoPopupStore } from '../stores/jackpotInfoPopupStore'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const jackpotInfoPopupStore = useJackpotInfoPopupStore()
const router = useRouter()
function goToEarnChest() {
  audioService.play('button1')
  router.push("referral")
}

const OpenPopup = () => {
  audioService.play('button1'); // Play sound effect
  jackpotInfoPopupStore.openJackpotInfoPopup()
}
</script>

<template>
    <div class="panel-container">
        <div class="panel-border-wrap">
            <div class="panel-upper">
                <div class="panel-row-1">
                    <div class="panel-heading">
                        {{ $t('common.jackpotChest') }}
                    </div>
                    <div class="button-group">
                        <button class="button" @click="OpenPopup">
                            <img src="/ui/left-arrow-purple.png" class="triangle"></img>
                            <span class="button-text"> {{ $t('chest.info') }}</span>
                            <img src="/ui/right-arrow-purple.png"class="triangle"></img>
                        </button>
                    </div>
                </div>
                <div class="panel-content">
                    <img src="/icon/chest.png" class="chest-image"></img>
                    <div class="panel-content-box">
                        <div class="panel-title">{{ $t('common.jackpotChest') }}</div>
                        <div class="panel-description">
                            <div>{{ $t('common.excitingRewardsAwait') }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-lower">
                <OrangeGradientButton @click="goToEarnChest">{{ $t('common.earnChest') }}</OrangeGradientButton>
            </div>
        </div>

    </div>    
</template>

<style scoped>
.panel-container {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.panel-border-wrap {
    width: 100%;
    background: linear-gradient(180deg, #DB84FF 0%, #824BEC 100%);
    padding: calc(var(--base-unit) * 2);
    box-sizing: border-box;
    border-radius: calc(var(--base-unit) * 12);
}

.panel-upper {
    width: 100%;
    background: linear-gradient(112.75deg, #C57CFF 3.36%, #7856F2 78.51%);
    border-radius: calc(var(--base-unit) * 12) calc(var(--base-unit) * 12) 0 0;
}

.panel-lower {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    padding: calc(var(--base-unit) * 8) calc(var(--base-unit) * 16);
    background: linear-gradient(180deg, #6038C7 0%, #5F37C3 100%);
    border-radius: 0 0 calc(var(--base-unit) * 12) calc(var(--base-unit) * 12);
    box-sizing: border-box;
}

.panel-row-1 {
    display: flex;
    justify-content: space-between;
    padding: calc(var(--base-unit) * 12);
}

.panel-heading {
    color: #FFFFFF;
    font-size: calc(var(--base-unit) * 24);
    paint-order: stroke fill;
    -webkit-text-stroke: calc(var(--base-unit) * 2) #544949;
    text-shadow: 0 calc(var(--base-unit) * 1) calc(var(--base-unit) * 2) rgba(0, 0, 0, 0.25);
}

.button-group {
    display: flex;
    justify-content: space-between;
}

.button {
    color: #5925DC;
    border-radius: calc(var(--base-unit) * 12);
    height: calc(var(--base-unit) * 22);
    border: calc(var(--base-unit) * 1) solid #785890;
    padding: calc(var(--base-unit) * 4) calc(var(--base-unit) * 6);

    display: flex;
    justify-content: space-between;
    gap: calc(var(--base-unit) * 8);
    transition: transform 0.1s ease, box-shadow 0.1s ease;
}

.button:active {
    transform: scale(0.95); /* Slightly shrink button */
}

.triangle {
    width: calc(var(--base-unit) * 4);
    height: 100%;
    object-fit: contain;
}

.panel-content {
    display: flex;
    justify-content: space-between;
    padding: calc(var(--base-unit) * 12);
}

.chest-image {
    width: calc(var(--base-unit) * 110);
    object-fit: contain;
}

.panel-content-box {
    display: flex;
    flex-direction: column;
    padding: calc(var(--base-unit) * 12);
        gap: calc(var(--base-unit) * 8);
}

.panel-title {
    color: #FFFFFF;
    font-size: calc(var(--base-unit) * 24);
    paint-order: stroke fill;
    -webkit-text-stroke: calc(var(--base-unit) * 2.4) #812F50;
    text-shadow: 0 calc(var(--base-unit) * 1) calc(var(--base-unit) * 2) rgba(0, 0, 0, 0.25);
}

.panel-description {
    color: #FDEDA7;
    font-size: calc(var(--base-unit) * 12);
    text-shadow: 0 calc(var(--base-unit) * 1) calc(var(--base-unit) * 4) rgba(0, 0, 0, 0.39);
    line-height: 1.5;
}

</style>