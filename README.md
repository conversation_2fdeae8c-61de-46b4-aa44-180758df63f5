# MooFun Project

A modern web application built with Vue 3, Phaser, and PixiJS for interactive experiences.

## 🚀 Features

- Vue 3 for reactive UI components
- Phaser and PixiJS integration for gaming and animations
- Spine animation support
- TON blockchain integration
- Internationalization support with vue-i18n
- State management with Pinia
- Built with Vite for optimal development experience

## 📋 Prerequisites

Before you begin, ensure you have the following installed:
- Node.js (v16.0.0 or higher recommended)
- npm (v7.0.0 or higher)

## 🛠️ Installation

1. Clone the repository:
```bash
git clone https://github.com/your-username/moofun.git
cd moofun
```

2. Install dependencies:
```bash
npm install
```

## 🏃‍♂️ Development

To start the development server:
```bash
npm run dev
```

The application will be available at `http://localhost:5173` by default.

## 🏗️ Building for Production

To build the application for production:
```bash
npm run build
```

To preview the production build:
```bash
npm run preview
```

## 📁 Project Structure

```
moofun/
├── src/
│   ├── components/     # Reusable UI components
│   ├── features/       # Feature-specific modules
│   ├── api/           # API client and types
│   ├── utils/         # Shared utilities
│   └── styles/        # Global styles
└── public/            # Static assets
```

## 🔧 Configuration

The project uses the following main configuration files:
- `vite.config.mjs` - Vite configuration
- `tsconfig.json` - TypeScript configuration
- `package.json` - Project dependencies and scripts

## 📚 Documentation

For detailed documentation about specific components and features, please refer to:
- [Component Documentation](./docs/components.md)
- [API Integration Guide](./docs/api.md)
- [Game Development Guide](./docs/game.md)

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

Please make sure to update tests and documentation as appropriate.

## 📄 License

This project is licensed under the ISC License - see the [LICENSE](LICENSE) file for details.

## 📦 Dependencies

Key dependencies include:
- Vue 3
- Phaser
- PixiJS
- Spine for Phaser
- TON Connect UI
- Vue Router
- Pinia
- Vue I18n

For a complete list of dependencies, please refer to the `package.json` file.

## 🤝 Support

For support, please open an issue in the GitHub repository or contact the development team.