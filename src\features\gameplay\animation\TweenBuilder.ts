// TweenBuilder.ts
import { TweenManager } from './TweenManager';
import { Easing, EaseFunction } from './Tween';

export class TweenBuilder<T> {
  private target!: T;
  private from = 0;
  private to = 1;
  private duration = 1;
  private delay = 0;
  private ease: EaseFunction = Easing.Linear;
  private onComplete?: () => void;
  private onUpdate!: (target: T, value: number) => void;

  static update<T>(
    target: T,
    onUpdate: (target: T, value: number) => void,
    from: number,
    to: number,
    duration: number
  ): TweenBuilder<T> {
    const builder = new TweenBuilder<T>();
    builder.target = target;
    builder.onUpdate = onUpdate;
    builder.from = from;
    builder.to = to;
    builder.duration = duration;
    return builder;
  }

  setEase(ease: EaseFunction): this {
    this.ease = ease;
    return this;
  }

  setDelay(delay: number): this {
    this.delay = delay;
    return this;
  }

  setComplete(callback: () => void): this {
    this.onComplete = callback;
    return this;
  }

  play(): void {
    TweenManager.getInstance().create({
      target: this.target,
      duration: this.duration,
      delay: this.delay,
      ease: this.ease,
      onUpdate: (target, progress) => {
        if (!target) return;
        const value = this.from + (this.to - this.from) * progress;
        this.onUpdate(target, value);
      },
      onComplete: this.onComplete,
    });
  }
}
