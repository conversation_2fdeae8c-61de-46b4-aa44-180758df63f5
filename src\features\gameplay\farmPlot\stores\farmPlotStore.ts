// stores/farmPlotStore.ts
import { defineStore } from 'pinia';
import { getFarmPlots } from '@/features/gameplay/api';

export interface FarmPlotStats {
  plotNumber: number;
  level: number;
  milkProduction: number;      // how much milk is produced per interval
  productionSpeed: number;     // how many times per second production occurs
  barnCount: number;
  isUnlocked: boolean;
  upgradeCost: number;
  unlockCost: number;
  nextUpgradeGrowth: {
    nextMilkProduction: number;
    nextProductionSpeed: number;
    nextBarnCount: number;
  };
  boostMultiplier: number;
}

export const useFarmStore = defineStore('farm', {
  state: () => ({
    plots: [] as FarmPlotStats[],
  }),

  actions: {
    setFarmPlots(plots: FarmPlotStats[]) {
      this.plots = plots;
    },

    updatePlot(plotNumber: number, newStats: Partial<FarmPlotStats>) {
      const plot = this.plots.find(p => p.plotNumber === plotNumber);
      if (plot) Object.assign(plot, newStats);
    },

    async setFarmPlotsFromApi() {
      try {
        const res = await getFarmPlots();
        console.log(res);
        if (res && Array.isArray(res.farmPlots)) {
          const mapped = res.farmPlots.map(p => ({
            plotNumber: p.plotNumber,
            level: p.level,
            milkProduction: p.milkProduction,
            productionSpeed: p.productionSpeed,
            barnCount: p.barnCount,
            isUnlocked: p.isUnlocked,
            upgradeCost: p.upgradeCost,
            unlockCost: p.unlockCost,
            nextUpgradeGrowth: p.nextUpgradeGrowth,
            boostMultiplier: p.boostMultiplier,
          }));
          this.setFarmPlots(mapped);
        }
      } catch (err) {
        console.error('Failed to load farm plots from API:', err);
      }
    },

    simulateUpdates() {
      for (const plot of this.plots) {
        this.updatePlot(plot.plotNumber, {
          milkProduction: Math.floor(Math.random() * 10 * (plot.plotNumber || 1)) + 1,
          productionSpeed: Math.floor(Math.random() * 4) + 1,
          level: Math.floor(Math.random() * 5) + 1,
          barnCount: Math.floor(Math.random() * 8) + 1,
          isUnlocked: Math.random() > 0.5,
          upgradeCost: Math.floor(Math.random() * 1000) - 500,
          boostMultiplier: Math.floor(Math.random() * 2) + 1,
        });
      }
      setInterval(() => {
        for (const plot of this.plots) {
          this.updatePlot(plot.plotNumber, {
            milkProduction: Math.floor(Math.random() * 10 * (plot.plotNumber || 1)) + 1,
            productionSpeed: Math.floor(Math.random() * 4) + 1,
            level: Math.floor(Math.random() * 5) + 1,
            barnCount: Math.floor(Math.random() * 8) + 1,
            isUnlocked: Math.random() > 0.5,
            upgradeCost: Math.floor(Math.random() * 1000) - 500,
            boostMultiplier: Math.floor(Math.random() * 2) + 1,
          });
        }
      }, 5000);
    }
  },
});
