<template>
  <div class="preload-container">
    <Background :imagePath="'/img/intro.png'"/>
    <VersionText />
  </div>
</template>
  
<script>
import VersionText from '@/components/utility/VersionText.vue';
import { Background } from '@/components/common';

export default {
  name: "PreloadComponent",
  components: {
    VersionText,
    Background
  }
};
</script>

<style scoped>
.preload-container {
  position: relative;
  width: 100%;
  height: var(--app-height);
  overflow: hidden;
}
</style>