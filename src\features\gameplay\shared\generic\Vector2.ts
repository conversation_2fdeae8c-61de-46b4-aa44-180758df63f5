export class Vector2 {
    constructor(public x: number = 0, public y: number = 0) {}
  
    static zero(): Vector2 {
      return new Vector2(0, 0);
    }
  
    static one(): Vector2 {
      return new Vector2(1, 1);
    }
  
    add(other: Vector2): Vector2 {
      return new Vector2(this.x + other.x, this.y + other.y);
    }
  
    subtract(other: Vector2): Vector2 {
      return new Vector2(this.x - other.x, this.y - other.y);
    }
  
    // --- MULTIPLY OVERLOADS ---
    multiply(scalar: number): Vector2;
    multiply(other: Vector2): Vector2;
    multiply(arg: number | Vector2): Vector2 {
      if (typeof arg === 'number') {
        return new Vector2(this.x * arg, this.y * arg);
      } else {
        return new Vector2(this.x * arg.x, this.y * arg.y);
      }
    }

    // --- DIVIDE OVERLOADS ---
    divide(scalar: number): Vector2;
    divide(other: Vector2): Vector2;
    divide(arg: number | Vector2): Vector2 {
      if (typeof arg === 'number') {
        if (arg === 0) throw new Error('Division by zero');
        return new Vector2(this.x / arg, this.y / arg);
      } else {
        if (arg.x === 0 || arg.y === 0) throw new Error('Division by zero');
        return new Vector2(this.x / arg.x, this.y / arg.y);
      }
    }
  
    magnitude(): number {
      return Math.sqrt(this.x * this.x + this.y * this.y);
    }
  
    normalize(): Vector2 {
      const mag = this.magnitude();
      if (mag === 0) return Vector2.zero();
      return this.divide(mag);
    }
  
    distance(other: Vector2): number {
      return this.subtract(other).magnitude();
    }
  
    static lerp(from: Vector2, to: Vector2, t: number): Vector2 {
      t = Math.max(0, Math.min(1, t)); // Clamp t between 0 and 1
      return new Vector2(
        from.x + (to.x - from.x) * t,
        from.y + (to.y - from.y) * t
      );
    }
  
    clone(): Vector2 {
      return new Vector2(this.x, this.y);
    }
  
    equals(other: Vector2): boolean {
      return this.x === other.x && this.y === other.y;
    }
  
    toString(): string {
      return `Vector2(${this.x}, ${this.y})`;
    }
  } 