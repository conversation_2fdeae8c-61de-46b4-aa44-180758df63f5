<template>
  <div class="banner-wrapper">
    <div class="banner-container" 
         :style="{ 
           backgroundColor: backgroundColor,
           boxShadow: `0 calc(var(--base-unit) * -4) calc(var(--base-unit) * 2) ${shadowColor} inset`,
           border: `calc(var(--base-unit) * 2) solid ${outlineColor}`
         }">
      <div class="banner-content" :style="{ 
           webkitTextStroke: `calc(var(--base-unit) * 2) ${shadowColor}`
         }">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TitlePanel',
  props: {
    backgroundColor: {
      type: String,
      default: 'rgba(99, 197, 25, 1)' // Default green background
    },
    outlineColor: {
      type: String,
      default: 'rgba(255, 255, 255, 1)' // Default white outline
    },
    shadowColor: {
      type: String,
      default: 'rgba(206, 249, 174, 1)' // Default light green shadow
    },

  }
}
</script>

<style scoped>
.banner-wrapper {
  position: relative;
  height: calc(var(--base-unit) * 46);
}

.banner-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  border-radius: calc(var(--base-unit) * 16);
}

.banner-content {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: rgba(255, 255, 255, 1);
  font-size: calc(var(--base-unit) * 20);
  -webkit-text-stroke: 0;
  text-shadow: none;
}
</style>