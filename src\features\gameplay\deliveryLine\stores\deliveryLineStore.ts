// stores/deliveryLineStore.ts
import { defineStore } from 'pinia';
import { getDeliveryLine } from '@/features/gameplay/api';

export interface DeliveryLineStats {
  id: number;
  walletId: number;
  level: number;
  deliverySpeed: number;
  blockUnit: number;
  blockPrice: number;
  upgradeCost: number;
  lastDeliveryTime: string;
  pendingMilk: number;
  pendingBlocks: number;
  createdAt: string;
  updatedAt: string;
  speedGrowthPercentage: number;
  boostMultiplier: number;
}

export interface DeliveryLineTemporalStats {
  milkOperations: {
    produce: number;
    consume: number;
  };
  temporallyGem: number;
}

export const useDeliveryLineStore = defineStore('deliveryLine', {
  state: () => ({
    deliveryLine: null as DeliveryLineStats | null,
    temporalStats: {
      milkOperations: {
        produce: 0,
        consume: 0,
      },
      temporallyGem: 0,
    } as DeliveryLineTemporalStats
  }),

  actions: {
    setDeliveryLine(data: DeliveryLineStats) {
      this.deliveryLine = data;
      this.deliveryLine.pendingMilk = Number(this.deliveryLine.pendingMilk);

    },

    async fetchDeliveryLineFromApi() {
      try {
        const res = await getDeliveryLine();
        if (res?.deliveryLine) {
          this.setDeliveryLine(res.deliveryLine);
        } else {
          console.warn('Unexpected response from getDeliveryLine:', res);
        }
      } catch (err) {
        console.error('Failed to fetch delivery line from API:', err);
      }
    },

    simulateDeliveryLineUpdates() {
      if (!this.deliveryLine) {
        console.warn('Cannot simulate without deliveryLine data.');
        return;
      }

      setInterval(() => {
        if (this.deliveryLine) {
          this.deliveryLine.deliverySpeed = parseFloat((Math.random() * 10 + 1).toFixed(2));
          this.deliveryLine.blockPrice = parseFloat((Math.random() * 50 + 5).toFixed(2));
        }
      }, 5000);
    },
  },
});
