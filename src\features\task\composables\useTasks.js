import { ref, computed, onMounted } from 'vue'
import { fetchTaskList, completeTask } from '../api'
import { useChestOverlayStore } from '@/features/openChest'
import { getSocialLinks } from '@/utils/config'
import audioService from '@/lib/audioService'

export function useTasks() {
  const tasks = ref([])
  const loading = ref(false)
  const error = ref(null)

  const chestOverlayStore = useChestOverlayStore()

  const chestRewardMap = {
    1: 1,
    2: 2,
    3: 2
  }

  // ---- Task Filters ----
  const completedTasks = computed(() => tasks.value.filter(t => t.isCompleted))
  const availableTasks = computed(() => tasks.value.filter(t => t.canComplete))
  const dailyTasks = computed(() => tasks.value.filter(t => t.repeatInterval === 'day'))
  const oneTimeTasks = computed(() => tasks.value.filter(t => t.repeatInterval === 'once'))
  const getTaskById = (id) => tasks.value.find(t => t.id === id)

  // ---- Actions ----
  const fetchTasks = async () => {
    loading.value = true
    error.value = null
    try {
      const result = await fetchTaskList()
      console.log("fetchTaskList", result);
      if (Array.isArray(result)) {
        tasks.value = result
      } else {
        tasks.value = []
        throw new Error('Failed to fetch tasks.')
      }
    } catch (err) {
      console.error('fetchTasks error:', err)
      error.value = err.message
      tasks.value = []
    } finally {
      loading.value = false
    }
  }

  const handleTaskClick = async (taskId) => {
    const task = getTaskById(taskId)
    const socialLinks = getSocialLinks()

    if (taskId === 2) {
        window.open(socialLinks.telegram, '_blank')
    }

    if (taskId === 3) {
      window.open(socialLinks.twitter, '_blank')
    }

    if (!task || !task.canComplete || task.isCompleted) return

    audioService.play('button1')

    const result = await completeTask(taskId)
    console.log("completeTask", result);
    if (result) {
      await fetchTasks()
      if (result.chestReward) {
        chestOverlayStore.openChest({ ok: true, data: result.chestReward })
      }
    }
  }

  onMounted(() => {
    fetchTasks()
  })

  return {
    tasks,
    loading,
    error,
    dailyTasks,
    oneTimeTasks,
    handleTaskClick,
    chestRewardMap
  }
}
