import { defineStore } from 'pinia';
import { useDeliveryLineStore } from '@/features/gameplay/deliveryLine';
import { useFarmStore } from '@/features/gameplay/farmPlot';
import { useUserInfo } from '@/features/userInfo'
import { batchUpdateResources } from '@/features/gameplay/api'


export interface GameplayState {
  isActive: boolean;
  lastSyncTime: number;
  pendingChanges: boolean;
  syncInterval?: number | null;
}

export const useGameplayStateManager = defineStore('gameplayState', {
  state: (): GameplayState => ({
    isActive: false,
    lastSyncTime: 0,
    pendingChanges: false,
    syncInterval: null,
  }),

  actions: {
    // Called when entering gameplay
    async enterGameplay() {
      this.isActive = true;
      this.lastSyncTime = Date.now();
      
      // Fetch initial state from API
      await this.syncFromServer();
      
      // Start periodic syncing
      this.startPeriodicSync();
    },

    // Called when exiting gameplay
    async exitGameplay() {
      this.isActive = false;
      
      // Stop periodic syncing
      this.stopPeriodicSync();
      
      // Final sync to server
      await this.syncToServer();
    },

    // Sync data from server
    async syncFromServer() {
      try {
        const deliveryLineStore = useDeliveryLineStore();
        const farmStore = useFarmStore();
        const { fetchUserInfo } = useUserInfo();

        await Promise.all([
          deliveryLineStore.fetchDeliveryLineFromApi(),
          farmStore.setFarmPlotsFromApi(),
          fetchUserInfo()
        ]);

        this.lastSyncTime = Date.now();
        this.pendingChanges = false;
      } catch (error) {
        console.error('Failed to sync from server:', error);
      }
    },

    // Sync data to server with batch update
    async syncToServer() {
      try {
        const deliveryLineStore = useDeliveryLineStore();
        const farmStore = useFarmStore();

        // Prepare temporal stats for batch update
        const temporalStats = deliveryLineStore.temporalStats;
        
          // Prepare gem request (convert milk to gems if needed)
          const gemRequest = temporalStats.temporallyGem;

          // Prepare milk operations (add accumulated milk)
          const milkOperations = temporalStats.milkOperations;

          // Call batch update API
          const res = await batchUpdateResources(gemRequest, milkOperations);
          console.log("batchUpdateResources:",res);

          // Reset temporal stats after successful sync
          deliveryLineStore.temporalStats.milkOperations.produce = 0;
          deliveryLineStore.temporalStats.milkOperations.consume = 0;
          deliveryLineStore.temporalStats.temporallyGem = 0;

        // Refresh UI data after batch update
        await this.refreshUIData();

        this.pendingChanges = false;
        this.lastSyncTime = Date.now();
      } catch (error) {
        console.error('Failed to sync to server:', error);
      }
    },

    // Refresh UI data after batch update
    async refreshUIData() {
      try {
        const deliveryLineStore = useDeliveryLineStore();
        const farmStore = useFarmStore();
        const { fetchUserInfo } = useUserInfo();

        // Fetch updated data to refresh UI
        await Promise.all([
          deliveryLineStore.fetchDeliveryLineFromApi(),
          farmStore.setFarmPlotsFromApi(),
          fetchUserInfo()
        ]);

        console.log('UI data refreshed successfully');
      } catch (error) {
        console.error('Failed to refresh UI data:', error);
      }
    },

    // Mark that changes need to be synced
    markPendingChanges() {
      this.pendingChanges = true;
    },

    // Manual sync for testing/debugging
    async manualSync() {
      console.log('Manual sync triggered');
      await this.syncToServer();
    },

    startPeriodicSync() {
      // Sync every 30 seconds while gameplay is active
      this.syncInterval = setInterval(async () => {
        if (this.isActive) {
          console.log('Performing periodic sync...');
          await this.syncToServer();
        }
      }, 10000);
    },

    stopPeriodicSync() {
      if (this.syncInterval) {
        clearInterval(this.syncInterval);
        this.syncInterval = null;
      }
    }
  }
});