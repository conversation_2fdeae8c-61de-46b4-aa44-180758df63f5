// stores/notificationStore.js
import { defineStore } from 'pinia';

export const useNotificationStore = defineStore('notification', {
  state: () => ({
    notifications: []
  }),
  
  actions: {
    addNotification({ type = 'info', message, title, duration = 3000 }) {
      const id = Date.now();
      this.notifications.push({
        id,
        type,
        message,
        title,
        duration
      });

      // Auto remove notification after duration
      setTimeout(() => {
        this.removeNotification(id);
      }, duration);
    },

    removeNotification(id) {
      const index = this.notifications.findIndex(n => n.id === id);
      if (index !== -1) {
        this.notifications.splice(index, 1);
      }
    }
  }
});