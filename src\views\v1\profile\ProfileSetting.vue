<script setup>
import { AppRow, TitlePanelColor } from '@/components/common'
import { useLanguageSettings } from '@/features/setting'
import { useAudioSettings } from '@/features/setting'

const { toggleLanguage, currentLanguageDisplay, t } = useLanguageSettings()
const { toggleMute, muteStatus } = useAudioSettings()
</script>

<template>
  <div class="setting-container">
    <TitlePanelColor :variant="'purple'">
      {{ t('setting.title') }}
    </TitlePanelColor>

    <AppRow class="panel-container" :clickable="true" @click="toggleLanguage">
      <span class="left">{{ t('setting.selectLanguage') }}</span>
      <template #right>
        <span>{{ currentLanguageDisplay }}</span>
      </template>
    </AppRow>

    <AppRow class="panel-container" :clickable="true" @click="toggleMute">
      <span class="left">{{ t('setting.sound') }}</span>
      <template #right>
        <span>{{ t(muteStatus) }}</span>
      </template>
    </AppRow>
  </div>
</template>

<style scoped>
.setting-container {
  margin: calc(var(--base-unit) * 10) calc(var(--base-unit) * 20);
  margin-bottom: calc(var(--base-unit) * 20);
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 16);
}
</style>
