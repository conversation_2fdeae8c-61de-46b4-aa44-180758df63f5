<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getPurchaseHistory } from '@/features/iap'
import { TitlePanelColor } from '@/components/common'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const router = useRouter()
const historyData = ref([])
const loading = ref(false)
const error = ref(null)
const pagination = ref({
  total: 0,
  page: 1,
  limit: 20,
  totalPages: 0
})

const loadPaymentHistory = async (page = 1) => {
  loading.value = true
  error.value = null
  
  try {
    console.log('Loading payment history, page:', page)
    const response = await getPurchaseHistory(page, pagination.value.limit)
    console.log('API response:', response)
    
    if (!response || !response.purchases) {
      throw new Error('Invalid response format')
    }
    
    historyData.value = response.purchases.map((purchase, index) => {
      try {
        return {
          id: purchase.paymentId || `purchase-${purchase.id}`,
          productName: purchase.IapProduct?.name || `Product #${purchase.productId}`,
          amount: (purchase.amount || 0).toString(),
          currency: purchase.currency,
          status: purchase.status || 'unknown',
          date: purchase.purchaseDate ? new Date(purchase.purchaseDate).toLocaleString('zh-CN') : '未知时间',
          paymentMethod: purchase.paymentMethod === 'stripe' ? 'usd' : 'kaia',
          originalData: purchase
        }
      } catch (itemError) {
        console.error(`Error processing purchase item ${index}:`, itemError, purchase)
        return {
          id: `error-${purchase.id || index}`,
          productName: 'Error loading product',
          amount: '0',
          currency: 'USD',
          status: 'error',
          date: '未知时间',
          paymentMethod: 'usd',
          originalData: purchase
        }
      }
    })
    
    console.log('Processed history data:', historyData.value)
    
    // 更新分页信息
    if (response.pagination) {
      pagination.value = {
        ...response.pagination,
        page: page
      }
    } else {
      // 如果API没有返回分页信息，计算总页数
      const total = response.purchases.length
      const totalPages = Math.ceil(total / pagination.value.limit)
      pagination.value = {
        total: total,
        page: page,
        limit: pagination.value.limit,
        totalPages: Math.max(1, totalPages)
      }
    }
    
    console.log('Updated pagination:', pagination.value)
    
  } catch (err) {
    console.error('Failed to load payment history:', err)
    console.error('Error details:', err.message, err.stack)
    error.value = `Failed to load payment history: ${err.message}`
    historyData.value = []
    // 重置分页信息
    pagination.value = {
      total: 0,
      page: 1,
      limit: pagination.value.limit,
      totalPages: 0
    }
  } finally {
    loading.value = false
  }
}

const getStatusColor = (status) => {
  switch (status) {

    case 'CONFIRMED':
    case 'FINALIZED':
      return '#4CAF50'
    
    case 'CONFIRM_FAILED':
    case 'CHARGEBACK':
    case 'CANCELED':
      return '#F44336'
    
    case 'CREATED':
    case 'STARTED':
    case 'REGISTERED_ON_PG':
    case 'CAPTURED':
    case 'PENDING':
    case 'REFUNDED':
    default:
      return '#FF9800'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'CREATED': return t('history.created')
    case 'STARTED': return t('history.started')
    case 'REGISTERED_ON_PG': return t('history.registeredOnPg')
    case 'CAPTURED': return t('history.captured')
    case 'PENDING': return t('history.pending')
    case 'CONFIRMED': return t('history.confirmed')
    case 'FINALIZED': return t('history.finalized')
    case 'REFUNDED': return t('history.refunded')
    case 'CONFIRM_FAILED': return t('history.confirmFailed')
    case 'CANCELED': return t('history.canceled')
    case 'CHARGEBACK': return t('history.chargeback')
    default: return t('history.unknown')
  }
}

const loadNextPage = () => {
  console.log('Load next page clicked. Current page:', pagination.value.page, 'Total pages:', pagination.value.totalPages)
  if (pagination.value.page < pagination.value.totalPages) {
    loadPaymentHistory(pagination.value.page + 1)
  } else {
    console.log('Cannot load next page - already at last page')
  }
}

const loadPreviousPage = () => {
  console.log('Load previous page clicked. Current page:', pagination.value.page, 'Total pages:', pagination.value.totalPages)
  if (pagination.value.page > 1) {
    loadPaymentHistory(pagination.value.page - 1)
  } else {
    console.log('Cannot load previous page - already at first page')
  }
}

const goBack = () => {
  router.go(-1)
}

onMounted(() => {
  loadPaymentHistory()
})
</script>

<template>
  <div class="history-page">
    <!-- 页面标题 -->
    <div class="page-header">
      
      <TitlePanelColor class="page-title">
        {{ $t('history.title') }}
      </TitlePanelColor>
    </div>

    <div class="history-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="spinner"></div>
        <div class="loading-text">{{ $t('history.loading') }}</div>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="error-state">
        <div class="error-icon">⚠️</div>
        <div class="error-text">{{ error }}</div>
        <button class="retry-button" @click="() => loadPaymentHistory(1)">
          {{ $t('history.retry') }}
        </button>
      </div>
      
      <!-- 历史记录列表 -->
      <div v-else-if="historyData.length > 0" class="history-list">
        <div 
          v-for="item in historyData" 
          :key="item.id"
          class="history-item"
        >
          <div class="item-header">
            <div class="item-title">{{ item.productName }}</div>
            <div 
              class="item-status"
              :style="{ color: getStatusColor(item.status) }"
            >
              <span>{{ getStatusText(item.status) }}</span>
            </div>
          </div>
          
          <div class="item-details">
            <div class="detail-row">
              <span class="detail-label">{{ $t('history.amount') }}:</span>
              <span class="detail-value">{{ item.amount }} {{ item.currency }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">{{ $t('history.date') }}:</span>
              <span class="detail-value">{{ item.date }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">{{ $t('history.orderId') }}:</span>
              <span class="detail-value order-id">{{ item.id }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">{{ $t('history.method') }}:</span>
              <span class="detail-value">
                <img 
                  :src="`/iap/${item.paymentMethod}.png`" 
                  :alt="item.paymentMethod"
                  class="payment-method-icon"
                />
                <span style="padding-top: 4px;">{{ item.paymentMethod.toUpperCase() }}</span>
              </span>
            </div>
          </div>
        </div>
        
        <div v-if="pagination?.totalPages > 1" class="pagination">
          <button 
            :disabled="pagination?.page <= 1"
            @click="loadPreviousPage"
            class="pagination-btn"
          >
            ← {{ $t('history.previousPage') }}
          </button>
          
          <span class="pagination-info">
            {{ pagination?.page }} / {{ pagination?.totalPages }}
            ({{ $t('history.total') }}: {{ pagination?.total }})
          </span>
          
          <button 
            :disabled="pagination?.page >= pagination?.totalPages"
            @click="loadNextPage"
            class="pagination-btn"
          >
            {{ $t('history.nextPage') }} →
          </button>
        </div>
      </div>
      
      <div v-else class="empty-state">
        <div class="empty-icon">💳</div>
        <div class="empty-text">{{ $t('history.noHistory') }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.history-page {
  margin: calc(var(--base-unit) * 10) calc(var(--base-unit) * 20);
  margin-bottom: calc(var(--base-unit) * 20);
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 24);
  min-height: calc(var(--app-height) - calc(var(--base-unit) * 40));
}

.page-header {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 12);
}

.back-button {
  background: linear-gradient(45deg, #6B7280, #4B5563);
  color: white;
  border: calc(var(--base-unit) * 2) solid #FDD99B;
  border-radius: calc(var(--base-unit) * 8);
  padding: calc(var(--base-unit) * 8) calc(var(--base-unit) * 16);
  font-size: calc(var(--base-unit) * 14);
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s ease;
  text-align: center;
  align-self: flex-start;
}

.back-button:hover {
  transform: scale(1.02);
}

.back-button:active {
  transform: scale(0.98);
}

.page-title {
  margin-bottom: calc(var(--base-unit) * 8);
}

.history-container {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 16);
  width: 100%;
  max-width: calc(var(--base-unit) * 500);
  margin: 0 auto;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 32);
}

.spinner {
  display: inline-block;
  width: calc(var(--base-unit) * 32);
  height: calc(var(--base-unit) * 32);
  border: calc(var(--base-unit) * 3) solid rgba(161, 193, 230, 0.3);
  border-top: calc(var(--base-unit) * 3) solid #2D7CCD;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #FCF1CB;
  font-size: calc(var(--base-unit) * 14);
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 32);
}

.error-icon {
  font-size: calc(var(--base-unit) * 32);
}

.error-text {
  color: #FCF1CB;
  font-size: calc(var(--base-unit) * 14);
  text-align: center;
}

.retry-button {
  background: #F44336;
  color: white;
  border: none;
  border-radius: calc(var(--base-unit) * 6);
  padding: calc(var(--base-unit) * 8) calc(var(--base-unit) * 16);
  font-size: calc(var(--base-unit) * 12);
  cursor: pointer;
  transition: background 0.2s ease;
}

.retry-button:hover {
  background: #D32F2F;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 12);
}

.history-item {
  background: #CF8E61;
  border: calc(var(--base-unit) * 2) solid #FDD99B;
  border-radius: calc(var(--base-unit) * 8);
  padding: calc(var(--base-unit) * 16);
  transition: transform 0.2s ease;
}

.history-item:hover {
  transform: scale(1.01);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: calc(var(--base-unit) * 12);
}

.item-title {
  font-size: calc(var(--base-unit) * 18);
  font-weight: bold;
  color: #FCF1CB;
}

.item-status {
  font-size: calc(var(--base-unit) * 14);
  font-weight: bold;
  padding: calc(var(--base-unit) * 4) calc(var(--base-unit) * 8);
  border-radius: calc(var(--base-unit) * 6);
  background: rgba(0, 0, 0, 0.3);
  line-height: normal;
}

.item-details {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 6);
  line-height: normal;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: calc(var(--base-unit) * 14);
}

.detail-label {
  color: rgba(252, 241, 203, 0.8);
  font-weight: 500;
}

.detail-value {
  color: #FCF1CB;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: calc(var(--base-unit) * 4);
  padding-top: 2px;
}

.order-id {
  max-width: calc(var(--base-unit) * 200);
  text-align: right;
}

.payment-method-icon {
  width: calc(var(--base-unit) * 18);
  height: calc(var(--base-unit) * 18);
  object-fit: contain;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: calc(var(--base-unit) * 12);
  padding: calc(var(--base-unit) * 32);
}

.empty-icon {
  font-size: calc(var(--base-unit) * 48);
  opacity: 0.6;
}

.empty-text {
  color: rgba(252, 241, 203, 0.8);
  font-size: calc(var(--base-unit) * 14);
  text-align: center;
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: calc(var(--base-unit) * 16);
  padding: calc(var(--base-unit) * 12);
  background: rgba(0, 0, 0, 0.3);
  border-radius: calc(var(--base-unit) * 8);
  border: calc(var(--base-unit) * 1) solid #FDD99B;
}

.pagination-btn {
  background: #2D7CCD;
  color: white;
  border: none;
  border-radius: calc(var(--base-unit) * 6);
  padding: calc(var(--base-unit) * 8) calc(var(--base-unit) * 16);
  font-size: calc(var(--base-unit) * 14);
  cursor: pointer;
  transition: background 0.2s ease;
}

.pagination-btn:hover:not(:disabled) {
  background: #1E5A9C;
}

.pagination-btn:disabled {
  background: #666;
  cursor: not-allowed;
  opacity: 0.5;
}

.pagination-info {
  color: #FCF1CB;
  font-size: calc(var(--base-unit) * 14);
  text-align: center;
}
</style> 