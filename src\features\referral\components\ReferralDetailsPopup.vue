<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import {
  TabsLayout,
  TitlePanelColor,
  DataTable,
  AppPopup,
  AppPagination
} from '@/components/common'
import { useReferralDetails } from '../composables/useReferralDetails'

const props = defineProps<{ isOpen: boolean }>()
const emit = defineEmits<{ 'close': [] }>()
const closeOverlay = () => emit('close')

const { t } = useI18n()
const {
  activeTab,
  displayData,
  pagination,
  loading,
  handleTabChange,
  changePage
} = useReferralDetails(() => props.isOpen)

const columns = [
  { title: t('referral.columns.no'), key: 'id', width: '10%', class: 'text-center' },
  { title: t('referral.columns.user'), key: 'username', width: '30%', class: 'with-padding-left' },
  { title: t('referral.columns.weekBoost'), key: 'weekBoost', width: '30%', class: 'text-center' },
  { title: t('referral.columns.dayBoost'), key: 'dayBoost', width: '30%', class: 'text-center' }
]
</script>


<template>
  <AppPopup :isOpen="isOpen" @close="closeOverlay">
    <div class="popup-container">
      <TitlePanelColor variant="green">{{ t('referral.title') }}</TitlePanelColor>

      <TabsLayout
        :tabs="[{ title: t('referral.level1') }, { title: t('referral.level2') }]"
        :activeTab="activeTab"
        @tab-change="handleTabChange"
      />

      <DataTable :columns="columns" :data="displayData" :loading="loading" />

      <AppPagination
        :page="pagination.page"
        :total="pagination.total"
        :pageSize="pagination.pageSize"
        @update:page="changePage"
      />
    </div>
  </AppPopup>
</template>


<style scoped>
.popup-container {
  display: flex;
  flex-direction: column;
  gap: calc(var(--base-unit) * 16);
}
</style>
