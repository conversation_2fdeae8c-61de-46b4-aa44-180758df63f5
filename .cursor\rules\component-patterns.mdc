---
description: 
globs: 
alwaysApply: false
---
# Vue Component Patterns

## Component Structure
```vue
<script setup lang="ts">
// 1. Imports
import { ref, computed } from 'vue'
import type { PropType } from 'vue'
import { useFeature } from '@/features/feature'

// 2. Props
interface Props {
  label: string
  value: number
}

// 3. Composables
const { someData, someMethod } = useFeature()

// 4. Reactive state
const localState = ref(null)

// 5. Computed properties
const computedValue = computed(() => {
  // Computation logic
})

// 6. Methods
const handleEvent = () => {
  // Event handling logic
}
</script>

<template>
  <div class="component-name">
    <!-- Template content -->
  </div>
</template>

<style scoped>
/* Component styles */
</style>
```

## Component Guidelines
- Use script setup syntax
- Keep components focused and small
- Extract complex logic to composables
- Use proper TypeScript types
- Follow proper naming conventions
- Implement proper prop validation
- Use slots for flexible content

## State Management
- Use composables for complex state
- Keep local state minimal
- Use computed properties for derived state
- Implement proper reactivity
- Handle side effects properly

## Event Handling
- Use proper event naming
- Implement proper type safety
- Handle errors gracefully
- Use proper event modifiers
- Document complex event flows

## Props Pattern
```typescript
// Props interface pattern
interface Props {
  label: string
  value: number
  required?: boolean
  callback?: (value: string) => void
}

// Props with validation
const props = defineProps<Props>()
```

## Component Communication
- Use events for child-to-parent
- Use props for parent-to-child
- Use provide/inject for deep passing
- Document component interfaces
- Keep communication patterns consistent

## Styling Guidelines
- Use scoped styles
- Follow BEM naming convention
- Use CSS variables for theming
- Keep styles modular
- Implement responsive design

## Performance Optimization
- Use proper computed properties
- Implement proper memoization
- Use v-show vs v-if appropriately
- Lazy load components when needed
- Monitor component re-renders

## Component Testing
- Test component rendering
- Test user interactions
- Test prop validation
- Test event emission
- Mock complex dependencies

## Accessibility
- Use proper ARIA attributes
- Implement keyboard navigation
- Test with screen readers
- Follow accessibility guidelines
- Document accessibility features

