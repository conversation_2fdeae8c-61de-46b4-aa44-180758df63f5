<script setup>
import { onMounted, onUnmounted } from 'vue'
import { <PERSON><PERSON><PERSON>ou<PERSON> } from '@/features/userInfo'
import { DeliveryLinePanel, DeliveryLineUpgradePopup, DeliveryLine } from '@/features/gameplay/deliveryLine'
import { FarmGroup } from '@/features/gameplay/farmPlot'
import { BoosterInventoryPopup } from '@/features/gameplay/booster'
import { FarmPlotUpgradePopup } from '@/features/gameplay/farmPlot'
import { FarmPlotUnlockPopup } from '@/features/gameplay/farmPlot'
import { OfflineRewardPopup } from '@/features/gameplay/offline'
import { useGameplayStateManager } from '@/features/gameplay/stores/gameplayStateManager'
import TestBox from '@/features/gameplay/farmPlot/components/TestBox.vue'
import { detectPlatform } from '@/utils/platformDetection'
import { generateBoostLink, generateReferralLink } from '@/utils/config'

const gameplayStateManager = useGameplayStateManager()

onMounted(async () => {
  // Enter gameplay mode when component mounts
  await gameplayStateManager.enterGameplay()
  console.log('detectPlatform', detectPlatform());
  console.log('generateBoostLink', generateBoostLink('BOOST123ABC', 'liff'));
  console.log('generateReferralLink', generateReferralLink('BOOST123ABC', 'auto'));
})

onUnmounted(async () => {
  // Exit gameplay mode when component unmounts
  await gameplayStateManager.exitGameplay()
})
</script>

<template>
  <div id="Home">
    <GemCounter></GemCounter>
    <DeliveryLinePanel/>
    <div class="game-area">
      <DeliveryLine/>
      <FarmGroup/>
    </div>
  </div>

  <BoosterInventoryPopup/>  
  <FarmPlotUpgradePopup/>
  <FarmPlotUnlockPopup/>
  <DeliveryLineUpgradePopup/>
  <OfflineRewardPopup/>
</template>

<style scoped>
#Home {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.game-area {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.delivery-line {
  height: calc(var(--base-unit) * 120);
  flex-shrink: 0;
}

.farm-group {
  flex: 1;
  overflow-y: auto;
}
</style>
