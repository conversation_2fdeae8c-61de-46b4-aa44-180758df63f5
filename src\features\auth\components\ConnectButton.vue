<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useWalletAuth } from "../composables/useWalletAuth"
import { useUser } from "../composables/useUser"
import { useToken } from "../composables/useToken"
import audioService from '@/lib/audioService'
import { useNotificationStore } from '@/stores/notificationStore.js';
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

const { t } = useI18n();

const { connect, disconnect, loadConnection, isConnected } = useWalletAuth()
const { user, loadUser } = useUser();
const { token, loadToken } = useToken();
const notificationStore = useNotificationStore();
const router = useRouter()

// connect
const connectWallet = async () => {
  audioService.play('button1'); // Play sound effect
  connect();
};

// disconnect
const disconnectWallet = async () => {
  audioService.play('button1'); // Play sound effect
  await disconnect();
  showDropdown.value = false;
  notificationStore.addNotification({
      type: 'info',
      message: t('wallet.disconnected'),
      duration: 2000
  });
  router.push('/authentication')
};

// dropdown
const showDropdown = ref(false);
const dropdownContainer = ref(null);
const handleClickOutside = (event) => {
  if (dropdownContainer.value && !dropdownContainer.value.contains(event.target))
  {
    showDropdown.value = false;
  }
};

const toggleDropdown = () => {
  audioService.play('button1'); // Play sound effect
  showDropdown.value = !showDropdown.value;
};

// copy
let copiedMessage = ref("");
const copyAddress = async () => {
  audioService.play('button1'); // Play sound effect

  console.log(user.value ? user.value : "not connected");
  navigator.clipboard.writeText(user.value.walletAddress);
  copiedMessage.value = t('wallet.addressCopied');
  setTimeout(() => copiedMessage.value = "", 1000);
  
  notificationStore.addNotification({
      type: 'success',
      message: t('wallet.addressCopiedNotification'),
      duration: 2000
  });
}




onMounted(async () => {
  document.addEventListener('click', handleClickOutside)
  await loadConnection()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div class="connect-button-container" ref="dropdownContainer">
    <button class="connect-button main-text" @click="isConnected ? toggleDropdown() : connectWallet()">
      {{ isConnected ? user?.walletAddress?.slice(0, 6) + '...' + user?.walletAddress?.slice(-4)  : t('wallet.connect') }}
    </button>

    <transition name="fade-slide">
      <div v-if="showDropdown" class="dropdown-menu">
        <div class="dropdown-item" @click="copyAddress">
          {{ copiedMessage ? t('wallet.addressCopied') : t('wallet.copyAddress') }}
        </div>
        <div class="dropdown-item" @click="disconnectWallet()">
          {{ t('wallet.disconnect') }}
        </div>
      </div>
    </transition>
  </div>
</template>

<style scoped>
.connect-button-container {
  position: relative;
  display: inline-block;
  z-index: 10;
}

.connect-button {
  width: calc(var(--base-unit) * 128);
  height: calc(var(--base-unit) * 40);
  background-color: rgba(10, 189, 93, 1);
  box-shadow: 0px calc(var(--base-unit) * -4) 0px rgba(8, 3, 10, 0.33) inset;
  border: none;
  border-radius: calc(var(--base-unit) * 8);
  padding: calc(var(--base-unit) * 14) calc(var(--base-unit) * 10);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.15s ease-out;
  font-size: calc(var(--base-unit) * 14);
  color: white;
  cursor: pointer;
  z-index: 10;
}

.connect-button:active {
  transform: scale(0.98);
}

.dropdown-menu {
  position: absolute;
  top: 110%;
  background-color: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: calc(var(--base-unit) * 8);
  width: calc(var(--base-unit) * 128);
  box-shadow: 0 calc(var(--base-unit) * 4) calc(var(--base-unit) * 6) rgba(0, 0, 0, 0.3);
  z-index: 10;
  opacity: 1;
}

.dropdown-item {
  font-size: calc(var(--base-unit) * 14);
  padding: calc(var(--base-unit) * 12) 0;
  cursor: pointer;
  transition: background-color 0.2s;
  text-align: center;
}

.dropdown-item:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateY(calc(var(--base-unit) * -10));
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateY(calc(var(--base-unit) * -10));
}
</style>
