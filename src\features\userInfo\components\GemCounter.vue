<script setup>
import { onMounted } from 'vue'
import { useUserInfo } from '@/features/userInfo'

import { useDeliveryLineStore } from '@/features/gameplay/deliveryLine/stores/deliveryLineStore'

const { userInfo, fetchUserInfo } = useUserInfo()
const deliveryLineStore = useDeliveryLineStore();


onMounted(() => {
  fetchUserInfo()
})
</script>

<template>
  <div class="panel-container">
    <img class="gem-icon" src="/icon/gem.png" alt="gem" />
    <div class="panel">
      <div class="pattern-overlay"></div>
      <div class="content-wrapper">
        {{ (Number(userInfo?.gem || 0) + (Number(deliveryLineStore.temporalStats.temporallyGem) || 0)).toFixed() }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.panel-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: calc(var(--base-unit) * 18);
}

.panel {
  position: relative;
  width: calc(var(--base-unit) * 343);
  height: calc(var(--base-unit) * 48);
  background-image: linear-gradient(to right, #b15bf2, #5d099d);
  transform: skew(-10deg);
  padding: calc(var(--base-unit) * 8) calc(var(--base-unit) * 16);
  box-sizing: border-box;
  border-radius: calc(var(--base-unit) * 8);
  border: calc(var(--base-unit) * 2) solid black;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.gem-icon {
  position: absolute;
  top: calc(var(--base-unit) * 0);
  left: calc(var(--base-unit) * 12);
  width: calc(var(--base-unit) * 55);
  height: calc(var(--base-unit) * 55);
  object-fit: contain;
  z-index: 2;
}

.pattern-overlay {
  position: absolute;
  right: 0;
  top: 0;
  width: calc(var(--base-unit) * 100);
  height: 100%;
  background: url('/ui/right-pattern-purple.png') no-repeat right center;
  background-size: contain;
  pointer-events: none;
}

.content-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  transform: skew(10deg);
  width: 100%;
  color: white;
  font-size: calc(var(--base-unit) * 18);
  -webkit-text-stroke: calc(var(--base-unit) * 1) rgba(59, 59, 59, 1);
  text-shadow: none;
}
</style>
