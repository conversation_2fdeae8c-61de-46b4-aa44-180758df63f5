<!-- GameNotification.vue -->
<template>
    <Transition name="notification">
      <div 
        v-if="storeNotifications.length > 0" 
        class="notifications-container"
      >
        <TransitionGroup name="notification-list">
          <div
            v-for="notification in storeNotifications"
            :key="notification.id"
            :class="[
              'notification',
              `notification--${notification.type || 'info'}`
            ]"
          >
            <div class="notification__icon" v-if="notification.icon">
              <img :src="notification.icon" :alt="notification.type">
            </div>
            <div class="notification__content">
              <h4 v-if="notification.title" class="notification__title">
                {{ notification.title }}
              </h4>
              <p class="notification__message">{{ notification.message }}</p>
            </div>
          </div>
        </TransitionGroup>
      </div>
    </Transition>
</template>
  
<script setup>
import { storeToRefs } from 'pinia'
import { useNotificationStore } from '@/stores/notificationStore'

// Get the store and notifications
const notificationStore = useNotificationStore()
const { notifications: storeNotifications } = storeToRefs(notificationStore)

// For backward compatibility with direct component usage
const addNotification = (notification) => {
  notificationStore.addNotification(notification)
}

const removeNotification = (id) => {
  notificationStore.removeNotification(id)
}

// Expose methods to parent components
defineExpose({
  addNotification,
  removeNotification
})
</script>
  
<style scoped>
.notifications-container {
  position: fixed;
  top: calc(var(--base-unit) * 20);
  right: calc(var(--base-unit) * 20);
  left: calc(var(--base-unit) * 20);
  z-index: 9999;
}

.notification {
  display: flex;
  align-items: start;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: calc(var(--base-unit) * 15);
  margin-bottom: calc(var(--base-unit) * 10);
  border-radius: calc(var(--base-unit) * 8);
  backdrop-filter: blur(calc(var(--base-unit) * 5));
  box-shadow: 0 calc(var(--base-unit) * 4) calc(var(--base-unit) * 6) rgba(0, 0, 0, 0.1);
}

.notification__icon {
  margin-right: calc(var(--base-unit) * 12);
  width: calc(var(--base-unit) * 24);
  height: calc(var(--base-unit) * 24);
}

.notification__icon img {
  width: 100%;
  height: 100%;
}

.notification__content {
  flex: 1;
}

.notification__title {
  margin: 0 0 calc(var(--base-unit) * 5) 0;
  font-size: 1.1em;
  font-weight: bold;
}

.notification__message {
  margin: 0;
  font-size: calc(var(--base-unit) * 12);
}

/* Notification types */
.notification--success {
  background: rgba(40, 167, 69, 0.9);
}

.notification--error {
  background: rgba(220, 53, 69, 0.9);
}

.notification--warning {
  background: rgba(255, 193, 7, 0.9);
}

/* Animations */
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from,
.notification-leave-to {
  opacity: 0;
  transform: translateX(calc(var(--base-unit) * 30));
}

.notification-list-enter-active,
.notification-list-leave-active {
  transition: all 0.3s ease;
}

.notification-list-enter-from,
.notification-list-leave-to {
  opacity: 0;
  transform: translateY(calc(var(--base-unit) * -20));
}
</style>