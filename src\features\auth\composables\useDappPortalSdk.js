import DappPortalSDK from '@linenext/dapp-portal-sdk'

let sdkInstance = null;

export const useDappPortalSdk = () => {
  const init = async () => {
    if (sdkInstance) return sdkInstance;

    if (!import.meta.env.VITE_DAPP_CLIENT_ID) {
      console.warn('[Missing Client ID]');
      return null;
    }

    sdkInstance = await DappPortalSDK.init({
      clientId: import.meta.env.VITE_DAPP_CLIENT_ID,
      // chainId: '8217', // KAIA mainnet
      chainId: '1001', // KAIROS testnet
      testMode: true, //import.meta.env.DEV,
    });

    return sdkInstance;
  };

  const requestWalletAccess = async () => {
    const sdk = await init();
    const provider = sdk.getWalletProvider();
    const accounts = await provider.request({ method: 'kaia_requestAccounts' });
    return accounts?.[0] || null;
  };

  const getWalletAddress = async () => {
    const sdk = await init();
    const provider = sdk.getWalletProvider();
    const accounts = await provider.request({ method: 'kaia_accounts' });
    return accounts?.[0] || null;
  };
  

  const disconnectWallet = async () => {
    const sdk = await init();
    const provider = sdk.getWalletProvider();
    await provider.disconnectWallet();
  };

  const signMessage = async (message, address) => {
    const sdk = await init();
    const provider = sdk.getWalletProvider();
    return provider.request({
      method: 'personal_sign',
      params: [message, address],
    });
  };

  const startPayment = async (paymentId) => {
    const sdk = await init();
    if (!sdk) {
      throw new Error('SDK not initialized');
    }
    
    try {

      const paymentProvider = sdk.getPaymentProvider();
      if (!paymentProvider) {
        throw new Error('Payment provider not available');
      }
      

      const result = await paymentProvider.startPayment(paymentId);
      return result;
    } catch (error) {
      console.error('StartPayment failed:', error);
      throw error;
    }
  };

  return {
    init,
    requestWalletAccess,
    getWalletAddress,
    signMessage,
    startPayment,
    disconnectWallet,
  };
};