import fetch from '@/lib/fetch'

export const getReferralStatus = () => {
  return fetch.get('/referral/status').then(res => res.data)
}

export const getReferralChestCount = () => {
  return fetch.get('/referral/referral-chests/count').then(res => res.data)
}

export const claimDailyReferralChest = () => {
  return fetch.post('/invite/claim-daily').then(res => res.data)
}

export const openReferralChest = () => {
  return fetch.post('/referral/open').then(res => res.data)
}

export const bindReferralCode = (code) => {
  return fetch.post('/referral/bind', { code }).then(res => res.data)
}

export const getReferralList = () => {
  return fetch.get('/referral/list').then(res => res.data)
}

export const getDownlineList = (level = 1, page = 1, pageSize = 20) => {
  return fetch.get('/referral/downline', {
    params: { level, page, pageSize }
  }).then(res => res.data)
}
